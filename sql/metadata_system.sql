-- 元数据管理系统数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `metadata_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `metadata_system`;

-- 1. 平台管理表
CREATE TABLE `platform` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform_code` varchar(50) NOT NULL COMMENT '平台编码(如:taob<PERSON>,jd,youzan)',
  `platform_name` varchar(100) NOT NULL COMMENT '平台名称',
  `platform_desc` text COMMENT '平台描述',
  `api_version` varchar(20) COMMENT 'API版本',
  `status` tinyint DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `contact_info` json COMMENT '联系人信息',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_code` (`platform_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电商平台信息表';

-- 2. 数据类型表
CREATE TABLE `data_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type_code` varchar(50) NOT NULL COMMENT '数据类型编码(如:order,refund,product,category)',
  `type_name` varchar(100) NOT NULL COMMENT '数据类型名称',
  `type_desc` text COMMENT '数据类型描述',
  `parent_id` bigint DEFAULT 0 COMMENT '父类型ID(支持层级)',
  `level` int DEFAULT 1 COMMENT '层级深度',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`type_code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据类型表';

-- 3. 标准字段定义表
CREATE TABLE `standard_field` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `field_code` varchar(100) NOT NULL COMMENT '标准字段编码',
  `field_name` varchar(200) NOT NULL COMMENT '字段名称',
  `field_desc` text COMMENT '字段描述',
  `data_type_id` bigint NOT NULL COMMENT '所属数据类型ID',
  `field_type` varchar(50) NOT NULL COMMENT '字段数据类型(string,number,date,boolean,json)',
  `field_length` int COMMENT '字段长度',
  `is_required` tinyint DEFAULT 0 COMMENT '是否必填(1:是,0:否)',
  `is_unique` tinyint DEFAULT 0 COMMENT '是否唯一(1:是,0:否)',
  `default_value` varchar(500) COMMENT '默认值',
  `validation_rule` json COMMENT '校验规则(正则表达式、范围等)',
  `enum_values` json COMMENT '枚举值(如果是枚举类型)',
  `group_name` varchar(100) COMMENT '字段分组',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_code_type` (`field_code`, `data_type_id`),
  KEY `idx_data_type_id` (`data_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标准字段定义表';

-- 4. 平台字段映射表
CREATE TABLE `platform_field_mapping` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform_id` bigint NOT NULL COMMENT '平台ID',
  `data_type_id` bigint NOT NULL COMMENT '数据类型ID',
  `standard_field_id` bigint NOT NULL COMMENT '标准字段ID',
  `platform_field_name` varchar(200) NOT NULL COMMENT '平台原始字段名',
  `platform_field_path` varchar(500) COMMENT '字段路径(支持嵌套JSON)',
  `platform_field_type` varchar(50) COMMENT '平台字段类型',
  `transform_rule` json COMMENT '转换规则(格式化、计算公式等)',
  `sample_value` text COMMENT '示例值',
  `mapping_desc` text COMMENT '映射说明',
  `is_active` tinyint DEFAULT 1 COMMENT '是否生效(1:是,0:否)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_field_mapping` (`platform_id`, `data_type_id`, `standard_field_id`),
  KEY `idx_platform_id` (`platform_id`),
  KEY `idx_standard_field_id` (`standard_field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台字段映射表';

-- 5. 数据字典表
CREATE TABLE `data_dictionary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dict_type` varchar(100) NOT NULL COMMENT '字典类型',
  `dict_code` varchar(100) NOT NULL COMMENT '字典编码',
  `dict_name` varchar(200) NOT NULL COMMENT '字典名称',
  `dict_value` varchar(500) COMMENT '字典值',
  `parent_code` varchar(100) COMMENT '父级编码',
  `platform_id` bigint COMMENT '关联平台ID(为空表示通用)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `remark` text COMMENT '备注',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dict_type_code` (`dict_type`, `dict_code`, `platform_id`),
  KEY `idx_dict_type` (`dict_type`),
  KEY `idx_platform_id` (`platform_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据字典表';

-- 6. 平台数据支持情况表
CREATE TABLE `platform_data_support` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform_id` bigint NOT NULL COMMENT '平台ID',
  `data_type_id` bigint NOT NULL COMMENT '数据类型ID',
  `support_status` tinyint NOT NULL COMMENT '支持状态(1:完全支持,2:部分支持,0:不支持)',
  `support_desc` text COMMENT '支持说明',
  `api_endpoint` varchar(500) COMMENT 'API接口地址',
  `sync_frequency` varchar(50) COMMENT '同步频率',
  `last_sync_time` datetime COMMENT '最后同步时间',
  `data_volume` bigint DEFAULT 0 COMMENT '数据量',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_data_type` (`platform_id`, `data_type_id`),
  KEY `idx_platform_id` (`platform_id`),
  KEY `idx_data_type_id` (`data_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台数据支持情况表';

-- 初始化数据
-- 插入平台数据
INSERT INTO `platform` (`platform_code`, `platform_name`, `platform_desc`, `api_version`, `status`, `contact_info`) VALUES
('taobao', '淘宝', '淘宝电商平台', 'v2.0', 1, '{"contact": "<EMAIL>", "doc": "https://open.taobao.com"}'),
('jd', '京东', '京东电商平台', 'v3.0', 1, '{"contact": "<EMAIL>", "doc": "https://open.jd.com"}'),
('youzan', '有赞', '有赞电商平台', 'v1.0', 1, '{"contact": "<EMAIL>", "doc": "https://www.youzanyun.com"}'),
('pdd', '拼多多', '拼多多电商平台', 'v1.0', 1, '{"contact": "<EMAIL>", "doc": "https://open.pinduoduo.com"}');

-- 插入数据类型
INSERT INTO `data_type` (`type_code`, `type_name`, `type_desc`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('order', '订单', '电商订单数据', 0, 1, 1, 1),
('refund', '退单', '退款退货数据', 0, 1, 2, 1),
('product', '商品', '商品信息数据', 0, 1, 3, 1),
('category', '类目', '商品分类数据', 0, 1, 4, 1),
('customer', '客户', '客户信息数据', 0, 1, 5, 1);

-- 插入标准字段定义 - 订单相关
INSERT INTO `standard_field` (`field_code`, `field_name`, `field_desc`, `data_type_id`, `field_type`, `is_required`, `group_name`, `sort_order`) VALUES
('order_id', '订单ID', '订单唯一标识', 1, 'string', 1, '基础信息', 1),
('order_no', '订单号', '订单编号', 1, 'string', 1, '基础信息', 2),
('order_status', '订单状态', '订单当前状态', 1, 'string', 1, '基础信息', 3),
('order_amount', '订单金额', '订单总金额', 1, 'number', 1, '金额信息', 4),
('payment_amount', '实付金额', '实际支付金额', 1, 'number', 1, '金额信息', 5),
('discount_amount', '优惠金额', '优惠减免金额', 1, 'number', 0, '金额信息', 6),
('buyer_id', '买家ID', '买家用户标识', 1, 'string', 1, '用户信息', 7),
('buyer_name', '买家姓名', '买家真实姓名', 1, 'string', 0, '用户信息', 8),
('create_time', '创建时间', '订单创建时间', 1, 'date', 1, '时间信息', 9),
('pay_time', '支付时间', '订单支付时间', 1, 'date', 0, '时间信息', 10);

-- 插入标准字段定义 - 商品相关
INSERT INTO `standard_field` (`field_code`, `field_name`, `field_desc`, `data_type_id`, `field_type`, `is_required`, `group_name`, `sort_order`) VALUES
('product_id', '商品ID', '商品唯一标识', 3, 'string', 1, '基础信息', 1),
('product_name', '商品名称', '商品标题名称', 3, 'string', 1, '基础信息', 2),
('product_price', '商品价格', '商品售价', 3, 'number', 1, '价格信息', 3),
('product_stock', '商品库存', '商品库存数量', 3, 'number', 1, '库存信息', 4),
('category_id', '分类ID', '商品分类标识', 3, 'string', 1, '分类信息', 5),
('brand_name', '品牌名称', '商品品牌', 3, 'string', 0, '品牌信息', 6),
('product_desc', '商品描述', '商品详细描述', 3, 'string', 0, '详情信息', 7),
('product_images', '商品图片', '商品图片列表', 3, 'json', 0, '详情信息', 8);
