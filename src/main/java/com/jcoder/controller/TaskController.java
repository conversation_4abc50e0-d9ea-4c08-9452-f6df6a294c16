package com.jcoder.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jcoder.dto.ApiResponse;
import com.jcoder.dto.CreateAuditTaskRequest;
import com.jcoder.entity.Task;
import com.jcoder.entity.TaskResult;
import com.jcoder.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 任务控制器
 */
@RestController
@RequestMapping("/task")
@CrossOrigin
public class TaskController {
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 分页查询任务列表
     */
    @GetMapping("/page")
    public ApiResponse<IPage<Task>> getTaskPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String taskType,
            @RequestParam(required = false) String status) {
        
        IPage<Task> result = taskService.getTaskPage(pageNum, pageSize, taskType, status);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询任务
     */
    @GetMapping("/{id}")
    public ApiResponse<Task> getTaskById(@PathVariable Long id) {
        Task task = taskService.getById(id);
        if (task != null) {
            return ApiResponse.success(task);
        } else {
            return ApiResponse.error("任务不存在");
        }
    }
    
    /**
     * 创建审核任务
     */
    @PostMapping("/audit")
    public ApiResponse<Task> createAuditTask(
            @RequestParam("taskName") String taskName,
            @RequestParam("brand") String brand,
            @RequestParam("regulationFileIds") String regulationFileIds,
            @RequestParam(value = "fileVersion", required = false) String fileVersion,
            @RequestParam(value = "ignoreDescription", defaultValue = "false") Boolean ignoreDescription,
            @RequestParam("auditFiles") List<MultipartFile> auditFiles) {
        try {
            CreateAuditTaskRequest request = new CreateAuditTaskRequest();
            request.setTaskName(taskName);
            request.setBrand(brand);
            request.setFileVersion(fileVersion);
            request.setIgnoreDescription(ignoreDescription);
            request.setAuditFiles(auditFiles);

            // 解析法规文件ID列表
            if (regulationFileIds != null && !regulationFileIds.isEmpty()) {
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                List<Long> ids = mapper.readValue(regulationFileIds,
                    mapper.getTypeFactory().constructCollectionType(List.class, Long.class));
                request.setRegulationFileIds(ids);
            }

            Task task = taskService.createAuditTask(request);
            return ApiResponse.success(task);
        } catch (Exception e) {
            return ApiResponse.error("创建审核任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建图片对比任务
     */
    @PostMapping("/image-compare")
    public ApiResponse<Task> createImageCompareTask(
            @RequestParam("taskName") String taskName,
            @RequestParam("file1") MultipartFile file1,
            @RequestParam("file2") MultipartFile file2) {
        
        try {
            Task task = taskService.createImageCompareTask(taskName, file1, file2);
            return ApiResponse.success(task);
        } catch (Exception e) {
            return ApiResponse.error("创建图片对比任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建图文对比任务
     */
    @PostMapping("/text-image-compare")
    public ApiResponse<Task> createTextImageCompareTask(
            @RequestParam("taskName") String taskName,
            @RequestParam("textFile") MultipartFile textFile,
            @RequestParam("imageFile") MultipartFile imageFile) {
        
        try {
            Task task = taskService.createTextImageCompareTask(taskName, textFile, imageFile);
            return ApiResponse.success(task);
        } catch (Exception e) {
            return ApiResponse.error("创建图文对比任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务报告
     */
    @GetMapping("/{id}/report")
    public ApiResponse<TaskResult> getTaskReport(@PathVariable Long id) {
        TaskResult result = taskService.getTaskReport(id);
        if (result != null) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.error("报告不存在");
        }
    }
    
    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteTask(@PathVariable Long id) {
        boolean success = taskService.removeById(id);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除任务失败");
        }
    }
}
