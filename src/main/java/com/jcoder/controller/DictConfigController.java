package com.jcoder.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jcoder.dto.ApiResponse;
import com.jcoder.entity.DictConfig;
import com.jcoder.service.DictConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典配置控制器
 */
@RestController
@RequestMapping("/dict")
@CrossOrigin
public class DictConfigController {
    
    @Autowired
    private DictConfigService dictConfigService;
    
    /**
     * 分页查询字典配置
     */
    @GetMapping("/page")
    public ApiResponse<IPage<DictConfig>> getDictPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String dictType) {
        
        Page<DictConfig> page = new Page<>(pageNum, pageSize);
        IPage<DictConfig> result = dictConfigService.page(page);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据字典类型查询字典列表
     */
    @GetMapping("/type/{dictType}")
    public ApiResponse<List<DictConfig>> getDictByType(@PathVariable String dictType) {
        List<DictConfig> list = dictConfigService.getByDictType(dictType);
        return ApiResponse.success(list);
    }
    
    /**
     * 获取所有字典类型
     */
    @GetMapping("/types")
    public ApiResponse<List<String>> getAllDictTypes() {
        List<String> types = dictConfigService.getAllDictTypes();
        return ApiResponse.success(types);
    }
    
    /**
     * 获取字典映射（用于前端下拉框等）
     */
    @GetMapping("/map/{dictType}")
    public ApiResponse<Map<String, String>> getDictMap(@PathVariable String dictType) {
        List<DictConfig> list = dictConfigService.getByDictType(dictType);
        Map<String, String> map = list.stream()
                .collect(Collectors.toMap(DictConfig::getDictKey, DictConfig::getDictValue));
        return ApiResponse.success(map);
    }
    
    /**
     * 新增字典配置
     */
    @PostMapping
    public ApiResponse<DictConfig> createDict(@RequestBody DictConfig dictConfig) {
        boolean success = dictConfigService.save(dictConfig);
        if (success) {
            return ApiResponse.success(dictConfig);
        } else {
            return ApiResponse.error("新增字典配置失败");
        }
    }
    
    /**
     * 更新字典配置
     */
    @PutMapping("/{id}")
    public ApiResponse<DictConfig> updateDict(@PathVariable Long id, @RequestBody DictConfig dictConfig) {
        dictConfig.setId(id);
        boolean success = dictConfigService.updateById(dictConfig);
        if (success) {
            return ApiResponse.success(dictConfig);
        } else {
            return ApiResponse.error("更新字典配置失败");
        }
    }
    
    /**
     * 删除字典配置
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDict(@PathVariable Long id) {
        boolean success = dictConfigService.removeById(id);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除字典配置失败");
        }
    }
}
