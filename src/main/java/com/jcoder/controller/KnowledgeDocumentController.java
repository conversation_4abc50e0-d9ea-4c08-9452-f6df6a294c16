package com.jcoder.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jcoder.dto.ApiResponse;
import com.jcoder.entity.KnowledgeDocument;
import com.jcoder.service.KnowledgeDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库文档控制器
 */
@RestController
@RequestMapping("/knowledge")
@CrossOrigin
public class KnowledgeDocumentController {
    
    @Autowired
    private KnowledgeDocumentService knowledgeDocumentService;
    
    /**
     * 分页查询知识库文档
     */
    @GetMapping("/page")
    public ApiResponse<IPage<KnowledgeDocument>> getDocumentPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String documentName,
            @RequestParam(required = false) String documentType) {
        
        IPage<KnowledgeDocument> result = knowledgeDocumentService.getDocumentPage(
                pageNum, pageSize, documentName, documentType);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询知识库文档
     */
    @GetMapping("/{id}")
    public ApiResponse<KnowledgeDocument> getDocumentById(@PathVariable Long id) {
        KnowledgeDocument document = knowledgeDocumentService.getById(id);
        if (document != null) {
            return ApiResponse.success(document);
        } else {
            return ApiResponse.error("文档不存在");
        }
    }
    
    /**
     * 上传知识库文档
     */
    @PostMapping("/upload")
    public ApiResponse<KnowledgeDocument> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("documentName") String documentName,
            @RequestParam("documentType") String documentType,
            @RequestParam(value = "documentVersion", required = false) String documentVersion,
            @RequestParam(value = "effectiveDate", required = false) String effectiveDate,
            @RequestParam(value = "remark", required = false) String remark) {
        
        try {
            KnowledgeDocument document = knowledgeDocumentService.uploadDocument(
                    file, documentName, documentType, documentVersion, effectiveDate, remark);
            return ApiResponse.success(document);
        } catch (Exception e) {
            return ApiResponse.error("上传文档失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新知识库文档信息
     */
    @PutMapping("/{id}")
    public ApiResponse<KnowledgeDocument> updateDocument(
            @PathVariable Long id, 
            @RequestBody KnowledgeDocument document) {
        
        document.setId(id);
        boolean success = knowledgeDocumentService.updateById(document);
        if (success) {
            return ApiResponse.success(document);
        } else {
            return ApiResponse.error("更新文档失败");
        }
    }
    
    /**
     * 删除知识库文档
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDocument(@PathVariable Long id) {
        boolean success = knowledgeDocumentService.deleteDocument(id);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除文档失败");
        }
    }
}
