package com.jcoder.controller;

import com.jcoder.dto.ApiResponse;
import com.jcoder.service.FileUploadService;
import com.jcoder.service.OssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/file")
@CrossOrigin
public class FileController {
    
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private OssService ossService;
    
    @Value("${food-agent.oss.enabled:false}")
    private boolean ossEnabled;
    
    /**
     * 获取文件上传配置信息
     */
    @GetMapping("/config")
    public ApiResponse<Map<String, Object>> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("ossEnabled", ossEnabled);
        config.put("storageType", ossEnabled ? "OSS" : "LOCAL");
        config.put("description", ossEnabled ? "文件将上传到阿里云OSS" : "文件将保存到本地存储");
        return ApiResponse.success(config);
    }
    
    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    public ApiResponse<Map<String, String>> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            String fileUrl = fileUploadService.uploadFile(file);
            String md5 = fileUploadService.calculateMD5(file);
            
            Map<String, String> result = new HashMap<>();
            result.put("fileUrl", fileUrl);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", String.valueOf(file.getSize()));
            result.put("fileMd5", md5);
            result.put("storageType", ossEnabled ? "OSS" : "LOCAL");
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public ApiResponse<Void> deleteFile(@RequestParam("fileUrl") String fileUrl) {
        try {
            boolean success = fileUploadService.deleteFile(fileUrl);
            if (success) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error("文件删除失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("文件删除失败: " + e.getMessage());
        }
    }
}
