package com.jcoder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务结果实体类
 */
@Data
@TableName("task_result")
public class TaskResult {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 结果类型：AUDIT_REPORT-审核报告，COMPARE_RESULT-对比结果
     */
    private String resultType;
    
    /**
     * 结果数据（JSON格式）
     */
    private String resultData;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
