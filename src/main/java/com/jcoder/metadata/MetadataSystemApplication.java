package com.jcoder.metadata;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 元数据管理系统启动类
 */
@SpringBootApplication
@MapperScan("com.jcoder.metadata.mapper")
public class MetadataSystemApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MetadataSystemApplication.class, args);
        System.out.println("元数据管理系统启动成功！");
        System.out.println("访问地址: http://localhost:8080/api");
    }
}
