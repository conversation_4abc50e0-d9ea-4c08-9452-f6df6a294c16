package com.jcoder.metadata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jcoder.metadata.entity.Platform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 平台信息Mapper接口
 */
@Mapper
public interface PlatformMapper extends BaseMapper<Platform> {
    
    /**
     * 分页查询平台信息
     */
    @Select("<script>" +
            "SELECT * FROM platform " +
            "WHERE status = 1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (platform_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR platform_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR platform_desc LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY " +
            "<if test='sortField != null and sortField != \"\"'>" +
            "#{sortField} #{sortOrder}, " +
            "</if>" +
            "created_time DESC" +
            "</script>")
    IPage<Platform> selectPageWithKeyword(Page<Platform> page, 
                                         @Param("keyword") String keyword,
                                         @Param("sortField") String sortField,
                                         @Param("sortOrder") String sortOrder);
    
    /**
     * 根据平台编码查询平台信息
     */
    @Select("SELECT * FROM platform WHERE platform_code = #{platformCode} AND status = 1")
    Platform selectByPlatformCode(@Param("platformCode") String platformCode);
    
    /**
     * 查询所有启用的平台
     */
    @Select("SELECT * FROM platform WHERE status = 1 ORDER BY sort_order ASC, created_time DESC")
    List<Platform> selectAllEnabled();
}
