package com.jcoder.metadata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jcoder.metadata.entity.StandardField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 标准字段Mapper接口
 */
@Mapper
public interface StandardFieldMapper extends BaseMapper<StandardField> {
    
    /**
     * 分页查询标准字段（带关联信息）
     */
    @Select("<script>" +
            "SELECT sf.*, dt.type_name as data_type_name " +
            "FROM standard_field sf " +
            "LEFT JOIN data_type dt ON sf.data_type_id = dt.id " +
            "WHERE sf.status = 1 " +
            "<if test='dataTypeId != null'>" +
            "AND sf.data_type_id = #{dataTypeId} " +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (sf.field_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR sf.field_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR sf.field_desc LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY " +
            "<if test='sortField != null and sortField != \"\"'>" +
            "sf.#{sortField} #{sortOrder}, " +
            "</if>" +
            "sf.sort_order ASC, sf.created_time DESC" +
            "</script>")
    IPage<StandardField> selectPageWithKeyword(Page<StandardField> page, 
                                              @Param("dataTypeId") Long dataTypeId,
                                              @Param("keyword") String keyword,
                                              @Param("sortField") String sortField,
                                              @Param("sortOrder") String sortOrder);
    
    /**
     * 根据数据类型ID查询标准字段
     */
    @Select("SELECT sf.*, dt.type_name as data_type_name " +
            "FROM standard_field sf " +
            "LEFT JOIN data_type dt ON sf.data_type_id = dt.id " +
            "WHERE sf.data_type_id = #{dataTypeId} AND sf.status = 1 " +
            "ORDER BY sf.sort_order ASC, sf.created_time DESC")
    List<StandardField> selectByDataTypeId(@Param("dataTypeId") Long dataTypeId);
    
    /**
     * 根据字段编码和数据类型查询标准字段
     */
    @Select("SELECT * FROM standard_field " +
            "WHERE field_code = #{fieldCode} AND data_type_id = #{dataTypeId} AND status = 1")
    StandardField selectByFieldCodeAndDataType(@Param("fieldCode") String fieldCode, 
                                               @Param("dataTypeId") Long dataTypeId);
    
    /**
     * 查询字段分组信息
     */
    @Select("SELECT DISTINCT group_name FROM standard_field " +
            "WHERE data_type_id = #{dataTypeId} AND status = 1 AND group_name IS NOT NULL " +
            "ORDER BY group_name")
    List<String> selectGroupsByDataType(@Param("dataTypeId") Long dataTypeId);
}
