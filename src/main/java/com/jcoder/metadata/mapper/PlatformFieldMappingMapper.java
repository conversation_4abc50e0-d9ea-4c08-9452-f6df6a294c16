package com.jcoder.metadata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jcoder.metadata.entity.PlatformFieldMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 平台字段映射Mapper接口
 */
@Mapper
public interface PlatformFieldMappingMapper extends BaseMapper<PlatformFieldMapping> {
    
    /**
     * 分页查询字段映射（带关联信息）
     */
    @Select("<script>" +
            "SELECT pfm.*, p.platform_name, dt.type_name as data_type_name, " +
            "sf.field_name as standard_field_name, sf.field_code as standard_field_code " +
            "FROM platform_field_mapping pfm " +
            "LEFT JOIN platform p ON pfm.platform_id = p.id " +
            "LEFT JOIN data_type dt ON pfm.data_type_id = dt.id " +
            "LEFT JOIN standard_field sf ON pfm.standard_field_id = sf.id " +
            "WHERE pfm.is_active = 1 " +
            "<if test='platformId != null'>" +
            "AND pfm.platform_id = #{platformId} " +
            "</if>" +
            "<if test='dataTypeId != null'>" +
            "AND pfm.data_type_id = #{dataTypeId} " +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (pfm.platform_field_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR sf.field_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR sf.field_code LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY " +
            "<if test='sortField != null and sortField != \"\"'>" +
            "pfm.#{sortField} #{sortOrder}, " +
            "</if>" +
            "pfm.created_time DESC" +
            "</script>")
    IPage<PlatformFieldMapping> selectPageWithKeyword(Page<PlatformFieldMapping> page, 
                                                      @Param("platformId") Long platformId,
                                                      @Param("dataTypeId") Long dataTypeId,
                                                      @Param("keyword") String keyword,
                                                      @Param("sortField") String sortField,
                                                      @Param("sortOrder") String sortOrder);
    
    /**
     * 根据平台和数据类型查询映射
     */
    @Select("SELECT pfm.*, p.platform_name, dt.type_name as data_type_name, " +
            "sf.field_name as standard_field_name, sf.field_code as standard_field_code " +
            "FROM platform_field_mapping pfm " +
            "LEFT JOIN platform p ON pfm.platform_id = p.id " +
            "LEFT JOIN data_type dt ON pfm.data_type_id = dt.id " +
            "LEFT JOIN standard_field sf ON pfm.standard_field_id = sf.id " +
            "WHERE pfm.platform_id = #{platformId} AND pfm.data_type_id = #{dataTypeId} " +
            "AND pfm.is_active = 1 " +
            "ORDER BY sf.sort_order ASC, pfm.created_time DESC")
    List<PlatformFieldMapping> selectByPlatformAndDataType(@Param("platformId") Long platformId, 
                                                           @Param("dataTypeId") Long dataTypeId);
    
    /**
     * 查询平台支持的字段映射
     */
    @Select("SELECT pfm.*, p.platform_name, dt.type_name as data_type_name, " +
            "sf.field_name as standard_field_name, sf.field_code as standard_field_code " +
            "FROM platform_field_mapping pfm " +
            "LEFT JOIN platform p ON pfm.platform_id = p.id " +
            "LEFT JOIN data_type dt ON pfm.data_type_id = dt.id " +
            "LEFT JOIN standard_field sf ON pfm.standard_field_id = sf.id " +
            "WHERE pfm.platform_id = #{platformId} AND pfm.is_active = 1 " +
            "ORDER BY dt.sort_order ASC, sf.sort_order ASC")
    List<PlatformFieldMapping> selectByPlatformId(@Param("platformId") Long platformId);
    
    /**
     * 检查映射是否存在
     */
    @Select("SELECT COUNT(*) FROM platform_field_mapping " +
            "WHERE platform_id = #{platformId} AND data_type_id = #{dataTypeId} " +
            "AND standard_field_id = #{standardFieldId}")
    int checkMappingExists(@Param("platformId") Long platformId, 
                          @Param("dataTypeId") Long dataTypeId,
                          @Param("standardFieldId") Long standardFieldId);
}
