package com.jcoder.metadata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jcoder.metadata.entity.DataType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据类型Mapper接口
 */
@Mapper
public interface DataTypeMapper extends BaseMapper<DataType> {
    
    /**
     * 分页查询数据类型
     */
    @Select("<script>" +
            "SELECT * FROM data_type " +
            "WHERE status = 1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (type_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR type_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR type_desc LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY " +
            "<if test='sortField != null and sortField != \"\"'>" +
            "#{sortField} #{sortOrder}, " +
            "</if>" +
            "sort_order ASC, created_time DESC" +
            "</script>")
    IPage<DataType> selectPageWithKeyword(Page<DataType> page, 
                                         @Param("keyword") String keyword,
                                         @Param("sortField") String sortField,
                                         @Param("sortOrder") String sortOrder);
    
    /**
     * 根据类型编码查询数据类型
     */
    @Select("SELECT * FROM data_type WHERE type_code = #{typeCode} AND status = 1")
    DataType selectByTypeCode(@Param("typeCode") String typeCode);
    
    /**
     * 查询所有启用的数据类型
     */
    @Select("SELECT * FROM data_type WHERE status = 1 ORDER BY sort_order ASC, created_time DESC")
    List<DataType> selectAllEnabled();
    
    /**
     * 查询树形结构数据类型
     */
    @Select("SELECT * FROM data_type WHERE status = 1 ORDER BY parent_id ASC, sort_order ASC")
    List<DataType> selectTreeData();
}
