package com.jcoder.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 平台字段映射实体类
 */
@TableName(value = "platform_field_mapping", autoResultMap = true)
public class PlatformFieldMapping {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long platformId;
    
    private Long dataTypeId;
    
    private Long standardFieldId;
    
    private String platformFieldName;
    
    private String platformFieldPath;
    
    private String platformFieldType;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> transformRule;
    
    private String sampleValue;
    
    private String mappingDesc;
    
    private Integer isActive;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    
    // 关联字段
    @TableField(exist = false)
    private String platformName;
    
    @TableField(exist = false)
    private String dataTypeName;
    
    @TableField(exist = false)
    private String standardFieldName;
    
    @TableField(exist = false)
    private String standardFieldCode;
    
    // 构造函数
    public PlatformFieldMapping() {}
    
    public PlatformFieldMapping(Long platformId, Long dataTypeId, Long standardFieldId, String platformFieldName) {
        this.platformId = platformId;
        this.dataTypeId = dataTypeId;
        this.standardFieldId = standardFieldId;
        this.platformFieldName = platformFieldName;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getPlatformId() {
        return platformId;
    }
    
    public void setPlatformId(Long platformId) {
        this.platformId = platformId;
    }
    
    public Long getDataTypeId() {
        return dataTypeId;
    }
    
    public void setDataTypeId(Long dataTypeId) {
        this.dataTypeId = dataTypeId;
    }
    
    public Long getStandardFieldId() {
        return standardFieldId;
    }
    
    public void setStandardFieldId(Long standardFieldId) {
        this.standardFieldId = standardFieldId;
    }
    
    public String getPlatformFieldName() {
        return platformFieldName;
    }
    
    public void setPlatformFieldName(String platformFieldName) {
        this.platformFieldName = platformFieldName;
    }
    
    public String getPlatformFieldPath() {
        return platformFieldPath;
    }
    
    public void setPlatformFieldPath(String platformFieldPath) {
        this.platformFieldPath = platformFieldPath;
    }
    
    public String getPlatformFieldType() {
        return platformFieldType;
    }
    
    public void setPlatformFieldType(String platformFieldType) {
        this.platformFieldType = platformFieldType;
    }
    
    public Map<String, Object> getTransformRule() {
        return transformRule;
    }
    
    public void setTransformRule(Map<String, Object> transformRule) {
        this.transformRule = transformRule;
    }
    
    public String getSampleValue() {
        return sampleValue;
    }
    
    public void setSampleValue(String sampleValue) {
        this.sampleValue = sampleValue;
    }
    
    public String getMappingDesc() {
        return mappingDesc;
    }
    
    public void setMappingDesc(String mappingDesc) {
        this.mappingDesc = mappingDesc;
    }
    
    public Integer getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public String getPlatformName() {
        return platformName;
    }
    
    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
    
    public String getDataTypeName() {
        return dataTypeName;
    }
    
    public void setDataTypeName(String dataTypeName) {
        this.dataTypeName = dataTypeName;
    }
    
    public String getStandardFieldName() {
        return standardFieldName;
    }
    
    public void setStandardFieldName(String standardFieldName) {
        this.standardFieldName = standardFieldName;
    }
    
    public String getStandardFieldCode() {
        return standardFieldCode;
    }
    
    public void setStandardFieldCode(String standardFieldCode) {
        this.standardFieldCode = standardFieldCode;
    }
}
