package com.jcoder.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 平台信息实体类
 */
@TableName(value = "platform", autoResultMap = true)
public class Platform {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String platformCode;
    
    private String platformName;
    
    private String platformDesc;
    
    private String apiVersion;
    
    private Integer status;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> contactInfo;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    
    // 构造函数
    public Platform() {}
    
    public Platform(String platformCode, String platformName) {
        this.platformCode = platformCode;
        this.platformName = platformName;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getPlatformCode() {
        return platformCode;
    }
    
    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }
    
    public String getPlatformName() {
        return platformName;
    }
    
    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
    
    public String getPlatformDesc() {
        return platformDesc;
    }
    
    public void setPlatformDesc(String platformDesc) {
        this.platformDesc = platformDesc;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Map<String, Object> getContactInfo() {
        return contactInfo;
    }
    
    public void setContactInfo(Map<String, Object> contactInfo) {
        this.contactInfo = contactInfo;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    @Override
    public String toString() {
        return "Platform{" +
                "id=" + id +
                ", platformCode='" + platformCode + '\'' +
                ", platformName='" + platformName + '\'' +
                ", platformDesc='" + platformDesc + '\'' +
                ", apiVersion='" + apiVersion + '\'' +
                ", status=" + status +
                ", contactInfo=" + contactInfo +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
