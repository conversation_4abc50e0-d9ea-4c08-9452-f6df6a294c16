package com.jcoder.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 标准字段定义实体类
 */
@TableName(value = "standard_field", autoResultMap = true)
public class StandardField {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String fieldCode;
    
    private String fieldName;
    
    private String fieldDesc;
    
    private Long dataTypeId;
    
    private String fieldType;
    
    private Integer fieldLength;
    
    private Integer isRequired;
    
    private Integer isUnique;
    
    private String defaultValue;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> validationRule;
    
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> enumValues;
    
    private String groupName;
    
    private Integer sortOrder;
    
    private Integer status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    
    // 关联字段
    @TableField(exist = false)
    private String dataTypeName;
    
    // 构造函数
    public StandardField() {}
    
    public StandardField(String fieldCode, String fieldName, Long dataTypeId, String fieldType) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.dataTypeId = dataTypeId;
        this.fieldType = fieldType;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFieldCode() {
        return fieldCode;
    }
    
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }
    
    public String getFieldName() {
        return fieldName;
    }
    
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String getFieldDesc() {
        return fieldDesc;
    }
    
    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }
    
    public Long getDataTypeId() {
        return dataTypeId;
    }
    
    public void setDataTypeId(Long dataTypeId) {
        this.dataTypeId = dataTypeId;
    }
    
    public String getFieldType() {
        return fieldType;
    }
    
    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }
    
    public Integer getFieldLength() {
        return fieldLength;
    }
    
    public void setFieldLength(Integer fieldLength) {
        this.fieldLength = fieldLength;
    }
    
    public Integer getIsRequired() {
        return isRequired;
    }
    
    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }
    
    public Integer getIsUnique() {
        return isUnique;
    }
    
    public void setIsUnique(Integer isUnique) {
        this.isUnique = isUnique;
    }
    
    public String getDefaultValue() {
        return defaultValue;
    }
    
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
    
    public Map<String, Object> getValidationRule() {
        return validationRule;
    }
    
    public void setValidationRule(Map<String, Object> validationRule) {
        this.validationRule = validationRule;
    }
    
    public List<String> getEnumValues() {
        return enumValues;
    }
    
    public void setEnumValues(List<String> enumValues) {
        this.enumValues = enumValues;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public String getDataTypeName() {
        return dataTypeName;
    }
    
    public void setDataTypeName(String dataTypeName) {
        this.dataTypeName = dataTypeName;
    }
}
