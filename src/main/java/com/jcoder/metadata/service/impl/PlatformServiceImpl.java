package com.jcoder.metadata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jcoder.metadata.common.PageQuery;
import com.jcoder.metadata.entity.Platform;
import com.jcoder.metadata.mapper.PlatformMapper;
import com.jcoder.metadata.service.PlatformService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 平台服务实现类
 */
@Service
public class PlatformServiceImpl extends ServiceImpl<PlatformMapper, Platform> implements PlatformService {
    
    @Override
    public IPage<Platform> selectPage(PageQuery pageQuery) {
        Page<Platform> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());
        return baseMapper.selectPageWithKeyword(page, pageQuery.getKeyword(), 
                                               pageQuery.getSortField(), pageQuery.getSortOrder());
    }
    
    @Override
    public Platform getByPlatformCode(String platformCode) {
        if (!StringUtils.hasText(platformCode)) {
            return null;
        }
        return baseMapper.selectByPlatformCode(platformCode);
    }
    
    @Override
    public List<Platform> getAllEnabled() {
        return baseMapper.selectAllEnabled();
    }
    
    @Override
    public boolean createPlatform(Platform platform) {
        // 检查平台编码是否已存在
        Platform existing = getByPlatformCode(platform.getPlatformCode());
        if (existing != null) {
            throw new RuntimeException("平台编码已存在: " + platform.getPlatformCode());
        }
        
        // 设置默认值
        if (platform.getStatus() == null) {
            platform.setStatus(1);
        }
        
        return save(platform);
    }
    
    @Override
    public boolean updatePlatform(Platform platform) {
        if (platform.getId() == null) {
            throw new RuntimeException("平台ID不能为空");
        }
        
        // 检查平台是否存在
        Platform existing = getById(platform.getId());
        if (existing == null) {
            throw new RuntimeException("平台不存在");
        }
        
        // 如果修改了平台编码，检查新编码是否已存在
        if (!existing.getPlatformCode().equals(platform.getPlatformCode())) {
            Platform codeExists = getByPlatformCode(platform.getPlatformCode());
            if (codeExists != null && !codeExists.getId().equals(platform.getId())) {
                throw new RuntimeException("平台编码已存在: " + platform.getPlatformCode());
            }
        }
        
        return updateById(platform);
    }
    
    @Override
    public boolean deletePlatform(Long id) {
        if (id == null) {
            throw new RuntimeException("平台ID不能为空");
        }
        
        Platform platform = getById(id);
        if (platform == null) {
            throw new RuntimeException("平台不存在");
        }
        
        // 逻辑删除：设置状态为0
        platform.setStatus(0);
        return updateById(platform);
    }
    
    @Override
    public boolean updateStatus(Long id, Integer status) {
        if (id == null || status == null) {
            throw new RuntimeException("参数不能为空");
        }
        
        Platform platform = getById(id);
        if (platform == null) {
            throw new RuntimeException("平台不存在");
        }
        
        platform.setStatus(status);
        return updateById(platform);
    }
}
