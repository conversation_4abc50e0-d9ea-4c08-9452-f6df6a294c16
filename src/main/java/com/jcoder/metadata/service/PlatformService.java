package com.jcoder.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jcoder.metadata.common.PageQuery;
import com.jcoder.metadata.entity.Platform;

import java.util.List;

/**
 * 平台服务接口
 */
public interface PlatformService extends IService<Platform> {
    
    /**
     * 分页查询平台信息
     */
    IPage<Platform> selectPage(PageQuery pageQuery);
    
    /**
     * 根据平台编码查询平台信息
     */
    Platform getByPlatformCode(String platformCode);
    
    /**
     * 查询所有启用的平台
     */
    List<Platform> getAllEnabled();
    
    /**
     * 创建平台
     */
    boolean createPlatform(Platform platform);
    
    /**
     * 更新平台
     */
    boolean updatePlatform(Platform platform);
    
    /**
     * 删除平台（逻辑删除）
     */
    boolean deletePlatform(Long id);
    
    /**
     * 启用/禁用平台
     */
    boolean updateStatus(Long id, Integer status);
}
