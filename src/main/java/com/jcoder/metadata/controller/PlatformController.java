package com.jcoder.metadata.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jcoder.metadata.common.PageQuery;
import com.jcoder.metadata.common.Result;
import com.jcoder.metadata.entity.Platform;
import com.jcoder.metadata.service.PlatformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台管理控制器
 */
@RestController
@RequestMapping("/platform")
@CrossOrigin(origins = "*")
public class PlatformController {
    
    @Autowired
    private PlatformService platformService;
    
    /**
     * 分页查询平台列表
     */
    @GetMapping("/page")
    public Result<IPage<Platform>> page(PageQuery pageQuery) {
        try {
            IPage<Platform> page = platformService.selectPage(pageQuery);
            return Result.success(page);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询所有启用的平台
     */
    @GetMapping("/list")
    public Result<List<Platform>> list() {
        try {
            List<Platform> platforms = platformService.getAllEnabled();
            return Result.success(platforms);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询平台详情
     */
    @GetMapping("/{id}")
    public Result<Platform> getById(@PathVariable Long id) {
        try {
            Platform platform = platformService.getById(id);
            if (platform == null) {
                return Result.notFound("平台不存在");
            }
            return Result.success(platform);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据平台编码查询平台信息
     */
    @GetMapping("/code/{platformCode}")
    public Result<Platform> getByCode(@PathVariable String platformCode) {
        try {
            Platform platform = platformService.getByPlatformCode(platformCode);
            if (platform == null) {
                return Result.notFound("平台不存在");
            }
            return Result.success(platform);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建平台
     */
    @PostMapping
    public Result<String> create(@RequestBody Platform platform) {
        try {
            boolean success = platformService.createPlatform(platform);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新平台
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody Platform platform) {
        try {
            platform.setId(id);
            boolean success = platformService.updatePlatform(platform);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除平台
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = platformService.deletePlatform(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新平台状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = platformService.updateStatus(id, status);
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }
}
