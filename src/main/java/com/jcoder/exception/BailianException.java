package com.jcoder.exception;

public class BailianException extends RuntimeException {

    public BailianException() {
        super();
    }

    public BailianException(String message) {
        super(message);
    }

    public BailianException(String message, Throwable cause) {
        super(message, cause);
    }

    public BailianException(Throwable cause) {
        super(cause);
    }

    protected BailianException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
