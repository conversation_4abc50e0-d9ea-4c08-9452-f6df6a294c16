package com.jcoder.exception;

public class QwenException extends RuntimeException {

    public QwenException() {
        super();
    }

    public QwenException(String message) {
        super(message);
    }

    public QwenException(String message, Throwable cause) {
        super(message, cause);
    }

    public QwenException(Throwable cause) {
        super(cause);
    }

    protected QwenException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
