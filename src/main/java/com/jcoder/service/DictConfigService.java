package com.jcoder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jcoder.entity.DictConfig;
import com.jcoder.mapper.DictConfigMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典配置服务
 */
@Service
public class DictConfigService extends ServiceImpl<DictConfigMapper, DictConfig> {
    
    /**
     * 根据字典类型查询字典列表
     */
    public List<DictConfig> getByDictType(String dictType) {
        QueryWrapper<DictConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dict_type", dictType)
                   .eq("status", 1)
                   .orderByAsc("sort_order");
        return list(queryWrapper);
    }
    
    /**
     * 根据字典类型和键查询字典值
     */
    public String getDictValue(String dictType, String dictKey) {
        QueryWrapper<DictConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dict_type", dictType)
                   .eq("dict_key", dictKey)
                   .eq("status", 1);
        DictConfig dictConfig = getOne(queryWrapper);
        return dictConfig != null ? dictConfig.getDictValue() : null;
    }
    
    /**
     * 获取所有字典类型
     */
    public List<String> getAllDictTypes() {
        return baseMapper.selectObjs(
            new QueryWrapper<DictConfig>()
                .select("DISTINCT dict_type")
                .eq("status", 1)
        ).stream()
        .map(Object::toString)
        .collect(java.util.stream.Collectors.toList());
    }
}
