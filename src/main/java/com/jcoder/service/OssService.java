package com.jcoder.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * OSS文件上传服务
 */
@Service
public class OssService {
    
    @Autowired
    private OSS ossClient;
    
    @Value("${food-agent.oss.bucket-name}")
    private String bucketName;
    
    @Value("${food-agent.oss.endpoint}")
    private String endpoint;
    
    @Value("${food-agent.oss.enabled:false}")
    private boolean ossEnabled;
    
    /**
     * 上传文件到OSS
     */
    public String uploadFile(MultipartFile file) throws IOException {
        if (!ossEnabled) {
            throw new RuntimeException("OSS未启用，请配置OSS相关参数");
        }
        
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
//        String fileName = generateFileName() + extension;
        
        // 按日期创建目录结构
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String objectKey = "food-agent/" + dateDir + "/" + originalFilename;
        
        try (InputStream inputStream = file.getInputStream()) {
            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, inputStream);
            
            // 上传文件
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            
            // 返回文件访问URL
            return objectKey;
        } catch (Exception e) {
            throw new IOException("上传文件到OSS失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除OSS文件
     */
    public boolean deleteFile(String fileUrl) {
        if (!ossEnabled) {
            return false;
        }

        try {
            // 从URL中提取objectKey
            String objectKey = extractObjectKeyFromUrl(fileUrl);
            if (objectKey != null) {
                ossClient.deleteObject(bucketName, objectKey);
                return true;
            }
        } catch (Exception e) {
            System.err.println("删除OSS文件失败: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 从URL中提取ObjectKey
     */
    private String extractObjectKeyFromUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return null;
        }
        
        try {
            // 假设URL格式为: https://bucket.endpoint/objectKey
            String prefix = "https://" + bucketName + "." + endpoint.replace("https://", "").replace("http://", "") + "/";
            if (fileUrl.startsWith(prefix)) {
                return fileUrl.substring(prefix.length());
            }
        } catch (Exception e) {
            System.err.println("解析文件URL失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 检查OSS是否可用
     */
    public boolean isOssEnabled() {
        return ossEnabled;
    }
}
