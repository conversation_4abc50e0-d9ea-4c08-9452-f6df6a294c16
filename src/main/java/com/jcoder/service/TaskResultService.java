package com.jcoder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jcoder.entity.TaskResult;
import com.jcoder.mapper.TaskResultMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 任务结果服务
 */
@Service
public class TaskResultService extends ServiceImpl<TaskResultMapper, TaskResult> {
    
    /**
     * 根据任务ID获取结果
     */
    public TaskResult getByTaskId(Long taskId) {
        QueryWrapper<TaskResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        return getOne(queryWrapper);
    }
    
    /**
     * 保存任务结果
     */
    public void saveTaskResult(Long taskId, String resultType, String resultData) {
        TaskResult taskResult = new TaskResult();
        taskResult.setTaskId(taskId);
        taskResult.setResultType(resultType);
        taskResult.setResultData(resultData);
        taskResult.setCreateTime(LocalDateTime.now());
        save(taskResult);
    }
}
