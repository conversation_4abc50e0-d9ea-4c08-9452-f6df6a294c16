package com.jcoder.service;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 文档解析服务
 */
@Service
public class DocumentParseService {
    
    /**
     * 解析文档内容
     */
    public List<String> parseDocument(String fileUrl) {
        // 模拟文档解析逻辑
        // 实际实现需要根据文件类型调用相应的解析工具
        
        String fileExtension = getFileExtension(fileUrl);
        
        switch (fileExtension.toLowerCase()) {
            case "pdf":
                return parsePdf(fileUrl);
            case "doc":
            case "docx":
                return parseWord(fileUrl);
            case "xls":
            case "xlsx":
                return parseExcel(fileUrl);
            case "ppt":
            case "pptx":
                return parsePowerPoint(fileUrl);
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
                return parseImage(fileUrl);
            default:
                return Arrays.asList("未知文件类型，无法解析");
        }
    }
    
    /**
     * 解析PDF文件
     */
    private List<String> parsePdf(String fileUrl) {
        // 模拟PDF解析
        return Arrays.asList(
            "产品名称：某某食品",
            "配料表：面粉、水、盐、添加剂",
            "营养成分表：能量1200kJ，蛋白质8g",
            "生产日期：2024-01-01",
            "保质期：12个月"
        );
    }
    
    /**
     * 解析Word文件
     */
    private List<String> parseWord(String fileUrl) {
        // 模拟Word解析
        return Arrays.asList(
            "食品包装设计说明",
            "产品规格：500g/袋",
            "包装材料：复合膜",
            "标签要求：符合GB 7718标准"
        );
    }
    
    /**
     * 解析Excel文件
     */
    private List<String> parseExcel(String fileUrl) {
        // 模拟Excel解析
        return Arrays.asList(
            "营养成分数据表",
            "能量：1200kJ/100g",
            "蛋白质：8g/100g",
            "脂肪：15g/100g",
            "碳水化合物：60g/100g"
        );
    }
    
    /**
     * 解析PowerPoint文件
     */
    private List<String> parsePowerPoint(String fileUrl) {
        // 模拟PPT解析
        return Arrays.asList(
            "产品包装设计方案",
            "设计理念：简约时尚",
            "色彩搭配：蓝白主调",
            "字体选择：黑体、宋体"
        );
    }
    
    /**
     * 解析图片文件
     */
    private List<String> parseImage(String fileUrl) {
        // 模拟图片OCR解析
        return Arrays.asList(
            "OCR识别文字：某某牌食品",
            "生产许可证号：SC12345678901234567",
            "执行标准：GB/T 20981",
            "净含量：500g"
        );
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileUrl) {
        if (fileUrl == null || fileUrl.lastIndexOf(".") == -1) {
            return "";
        }
        return fileUrl.substring(fileUrl.lastIndexOf(".") + 1);
    }
}
