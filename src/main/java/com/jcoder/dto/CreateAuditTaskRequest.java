package com.jcoder.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 创建审核任务请求
 */
@Data
public class CreateAuditTaskRequest {

    /**
     * 审核名称
     */
    @NotBlank(message = "审核名称不能为空")
    private String taskName;

    /**
     * 品牌
     */
    @NotBlank(message = "品牌不能为空")
    private String brand;

    /**
     * 法规文件ID列表
     */
    private List<Long> regulationFileIds;

    /**
     * 是否忽略描述信息
     */
    private Boolean ignoreDescription = false;

    /**
     * 文件版本
     */
    private String fileVersion;

    /**
     * 审核文件列表
     */
    private List<MultipartFile> auditFiles;
}
