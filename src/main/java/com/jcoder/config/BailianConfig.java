package com.jcoder.config;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.bailian20231229.Client;
import com.aliyun.sdk.service.bailian20231229.AsyncClient;
import com.aliyun.teaopenapi.models.Config;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * bailian配置
 */
@Configuration
@Getter
public class BailianConfig {

    @Value("${food-agent.bailian.access-key:}")
    private String accessKey;

    @Value("${food-agent.bailian.access-secret:}")
    private String accessScecret;

    @Value("${food-agent.bailian.access-region:}")
    private String accessRegion;

    @Value("${food-agent.bailian.knowledge-base-id:}")
    private String knowledgeBaseId;

    @Value("${food-agent.bailian.workspace-id:}")
    private String workspaceId;

    @Value("${food-agent.bailian.category-id:}")
    private String categoryId;

    @Bean
    public AsyncClient bailianClient() {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKey)
                .accessKeySecret(accessScecret)
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region(accessRegion) // Region ID
                //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/bailian
                                .setEndpointOverride("bailian.cn-beijing.aliyuncs.com")
                                .setConnectTimeout(Duration.ofSeconds(30))
                                .setResponseTimeout(Duration.ofMinutes(10))
                )
                .build();

        return client;
    }

    @Bean
    public Client bailianSyncClient() throws Exception {
        return new Client(new Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessScecret)
                .setEndpoint("bailian.cn-beijing.aliyuncs.com")
        );
    }

}
