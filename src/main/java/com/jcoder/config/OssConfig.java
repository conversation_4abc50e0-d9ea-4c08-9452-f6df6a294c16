package com.jcoder.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OSS配置
 */
@Configuration
@Getter
public class OssConfig {
    
    @Value("${food-agent.oss.endpoint}")
    private String endpoint;
    
    @Value("${food-agent.oss.access-key-id}")
    private String accessKeyId;
    
    @Value("${food-agent.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${food-agent.oss.access-region}")
    private String region;

    @Value("${food-agent.oss.bucket-name}")
    private String bucket;
    
    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}
