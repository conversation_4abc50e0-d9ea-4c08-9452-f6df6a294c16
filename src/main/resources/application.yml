server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: metadata-system

  # Database Configuration
  datasource:
    url: **********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 禁用SQL详细日志
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Logging Configuration
logging:
  level:
    com.jcoder: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    path: logs/
    name: logs/metadata-system.log
