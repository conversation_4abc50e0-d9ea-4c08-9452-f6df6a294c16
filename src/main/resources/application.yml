server:
  port: 8080

spring:
  application:
    name: aitest-project
  
  # Database Configuration
  datasource:
    url: *************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Logging Configuration
logging:
  level:
    com.jcoder.aitest: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
