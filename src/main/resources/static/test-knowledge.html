<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理测试页面</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>知识库管理测试页面</h1>

        <el-tabs v-model="activeTab">
            <el-tab-pane label="简化版测试" name="simple">
                <knowledge-management-simple></knowledge-management-simple>
            </el-tab-pane>
            <el-tab-pane label="完整版测试" name="full">
                <knowledge-management-fixed></knowledge-management-fixed>
            </el-tab-pane>
        </el-tabs>
    </div>

    <!-- 引入Vue和Element UI -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- 引入知识库管理组件 -->
    <script src="./js/components/knowledge-management-simple.js"></script>
    <script src="./js/components/knowledge-management-fixed.js"></script>
    
    <script>
        // 主应用
        new Vue({
            el: '#app',
            data() {
                return {
                    activeTab: 'simple'
                }
            },
            mounted() {
                // 设置axios默认配置
                axios.defaults.baseURL = '/api';
                axios.defaults.timeout = 30000;
                
                // 请求拦截器
                axios.interceptors.request.use(
                    config => {
                        return config;
                    },
                    error => {
                        return Promise.reject(error);
                    }
                );
                
                // 响应拦截器
                axios.interceptors.response.use(
                    response => {
                        return response.data;
                    },
                    error => {
                        this.$message.error('请求失败: ' + (error.response?.data?.message || error.message));
                        return Promise.reject(error);
                    }
                );
            }
        });
    </script>
</body>
</html>
