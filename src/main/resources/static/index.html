<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>食品智能审核系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header {
            height: 60px;
            background: #409EFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-container {
            flex: 1;
            display: flex;
        }
        .sidebar {
            width: 200px;
            background: #304156;
            transition: width 0.3s;
        }
        .sidebar.collapsed {
            width: 64px;
        }
        .content {
            flex: 1;
            padding: 20px;
            background: #f0f2f5;
            overflow-y: auto;
        }
        .menu-item {
            color: #bfcbd9;
        }
        .menu-item:hover {
            background-color: #263445;
        }
        .menu-item.is-active {
            background-color: #409EFF !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div style="display: flex; align-items: center;">
                    <el-button 
                        type="text" 
                        style="color: white; font-size: 18px; margin-right: 20px;"
                        @click="toggleSidebar">
                        <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
                    </el-button>
                    <h2 style="margin: 0;">食品智能审核系统</h2>
                </div>
                <div style="display: flex; align-items: center; gap: 20px;">
                    <!-- 存储配置信息 -->
                    <el-tooltip :content="storageInfo.description" placement="bottom">
                        <el-tag :type="storageInfo.storageType === 'OSS' ? 'success' : 'warning'" size="small">
                            {{ storageInfo.storageType === 'OSS' ? 'OSS存储' : '本地存储' }}
                        </el-tag>
                    </el-tooltip>

                    <el-dropdown>
                        <span style="color: white; cursor: pointer;">
                            <i class="el-icon-user"></i> 管理员
                            <i class="el-icon-arrow-down"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>个人设置</el-dropdown-item>
                            <el-dropdown-item>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
            
            <!-- 主体内容 -->
            <div class="main-container">
                <!-- 侧边栏 -->
                <div :class="['sidebar', { collapsed: sidebarCollapsed }]">
                    <el-menu
                        :default-active="activeMenu"
                        :collapse="sidebarCollapsed"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409EFF"
                        @select="handleMenuSelect">
                        
                        <el-menu-item index="audit" class="menu-item">
                            <i class="el-icon-document-checked"></i>
                            <span slot="title">审核工作台</span>
                        </el-menu-item>
                        
                        <el-menu-item index="compare" class="menu-item">
                            <i class="el-icon-copy-document"></i>
                            <span slot="title">版本差异对比</span>
                        </el-menu-item>
                        
                        <el-menu-item index="knowledge" class="menu-item">
                            <i class="el-icon-folder-opened"></i>
                            <span slot="title">知识库管理</span>
                        </el-menu-item>
                        
                        <el-menu-item index="system" class="menu-item">
                            <i class="el-icon-setting"></i>
                            <span slot="title">系统管理</span>
                        </el-menu-item>
                    </el-menu>
                </div>
                
                <!-- 内容区域 -->
                <div class="content">
                    <!-- 审核工作台 -->
                    <div v-if="activeMenu === 'audit'">
                        <audit-workspace></audit-workspace>
                    </div>
                    
                    <!-- 版本差异对比 -->
                    <div v-if="activeMenu === 'compare'">
                        <version-compare></version-compare>
                    </div>
                    
                    <!-- 知识库管理 -->
                    <div v-if="activeMenu === 'knowledge'">
                        <knowledge-management-fixed></knowledge-management-fixed>
                    </div>
                    
                    <!-- 系统管理 -->
                    <div v-if="activeMenu === 'system'">
                        <system-management></system-management>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue和Element UI -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- 引入组件 -->
    <script src="./js/components/audit-workspace.js"></script>
    <script src="./js/components/version-compare.js"></script>
    <script src="./js/components/knowledge-management-fixed.js"></script>
    <script src="./js/components/system-management.js"></script>
    <script src="./js/app.js"></script>
</body>
</html>
