// 修复版知识库管理组件
Vue.component('knowledge-management-fixed', {
    template: `
        <div>
            <el-card>
                <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>知识库文档管理</span>
                    <el-button type="primary" @click="showCreateDialog">
                        <i class="el-icon-plus"></i> 添加法规文档
                    </el-button>
                </div>
                
                <!-- 搜索条件 -->
                <div style="margin-bottom: 20px;">
                    <el-form :inline="true">
                        <el-form-item label="文档名称">
                            <el-input v-model="searchForm.documentName" placeholder="请输入文档名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="文档类型">
                            <el-select v-model="searchForm.documentType" placeholder="请选择文档类型" clearable>
                                <el-option v-for="type in documentTypes" :key="type.key" :label="type.value" :value="type.key"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="loadDocuments">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 文档列表 -->
                <el-table :data="documents" v-loading="loading">
                    <el-table-column prop="documentName" label="文档名称"></el-table-column>
                    <el-table-column prop="documentType" label="文档类型">
                        <template slot-scope="scope">
                            {{ getDocumentTypeText(scope.row.documentType) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="documentVersion" label="文档版本"></el-table-column>
                    <el-table-column prop="effectiveDate" label="生效日期"></el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                                {{ scope.row.status === 1 ? '有效' : '无效' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button type="text" @click="editDocument(scope.row)">编辑</el-button>
                            <el-button type="text" @click="downloadDocument(scope.row)">下载</el-button>
                            <el-button type="text" style="color: #f56c6c;" @click="deleteDocument(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="pagination.current"
                        :page-size="pagination.size"
                        :total="pagination.total"
                        layout="total, prev, pager, next">
                    </el-pagination>
                </div>
            </el-card>
            
            <!-- 上传文档对话框 -->
            <el-dialog :title="editMode ? '修改法规文档' : '添加法规文档'" :visible.sync="showUploadDialog" width="600px">
                <el-form :model="uploadForm" :rules="currentRules" ref="uploadForm" label-width="120px">
                    <el-form-item label="文档名称" prop="documentName">
                        <el-input v-model="uploadForm.documentName" placeholder="请输入文档名称"></el-input>
                    </el-form-item>
                    <el-form-item label="文档类型" prop="documentType">
                        <el-select v-model="uploadForm.documentType" placeholder="请选择文档类型">
                            <el-option v-for="type in documentTypes" :key="type.key" :label="type.value" :value="type.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="文档版本">
                        <el-input v-model="uploadForm.documentVersion" placeholder="请输入文档版本"></el-input>
                    </el-form-item>
                    <el-form-item label="生效日期">
                        <el-date-picker
                            v-model="uploadForm.effectiveDate"
                            type="date"
                            placeholder="请选择生效日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="uploadForm.status">
                            <el-radio :label="1">有效</el-radio>
                            <el-radio :label="0">无效</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input type="textarea" v-model="uploadForm.remark" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                    <el-form-item label="文档文件" v-if="!editMode" :required="true">
                        <el-upload
                            ref="fileUpload"
                            :auto-upload="false"
                            :file-list="uploadForm.fileList"
                            :on-change="handleFileChange"
                            :on-remove="handleFileRemove"
                            :limit="1"
                            drag>
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            <div class="el-upload__tip" slot="tip">支持pdf、word格式，文件大小不超过100MB</div>
                        </el-upload>
                        <div v-if="!editMode && uploadForm.fileList.length === 0 && showFileError" style="color: #f56c6c; font-size: 12px; margin-top: 5px;">
                            请上传文档文件
                        </div>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="closeDialog">取消</el-button>
                    <el-button type="primary" @click="submitDocument" :loading="uploading">
                        {{ editMode ? '保存' : '上传' }}
                    </el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            uploading: false,
            showUploadDialog: false,
            editMode: false,
            showFileError: false,
            documents: [],
            documentTypes: [],
            searchForm: {
                documentName: '',
                documentType: ''
            },
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            uploadForm: {
                id: null,
                documentName: '',
                documentType: '',
                documentVersion: '',
                effectiveDate: '',
                status: 1,
                remark: '',
                fileList: []
            }
        }
    },
    computed: {
        currentRules() {
            return {
                documentName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
                documentType: [{ required: true, message: '请选择文档类型', trigger: 'change' }]
            };
        }
    },
    mounted() {
        this.loadDocuments();
        this.loadDocumentTypes();
    },
    methods: {
        async loadDocuments() {
            this.loading = true;
            try {
                const response = await axios.get('/knowledge/page', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        documentName: this.searchForm.documentName,
                        documentType: this.searchForm.documentType
                    }
                });
                if (response.code === 200) {
                    this.documents = response.data.records || [];
                    this.pagination.total = response.data.total || 0;
                }
            } catch (error) {
                console.error('加载文档列表失败:', error);
                this.$message.error('加载文档列表失败');
            } finally {
                this.loading = false;
            }
        },
        async loadDocumentTypes() {
            try {
                const response = await axios.get('/dict/type/DOCUMENT_TYPE');
                if (response.code === 200) {
                    this.documentTypes = response.data.map(item => ({
                        key: item.dictKey,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载文档类型失败:', error);
            }
        },
        handlePageChange(page) {
            this.pagination.current = page;
            this.loadDocuments();
        },
        resetSearch() {
            this.searchForm.documentName = '';
            this.searchForm.documentType = '';
            this.pagination.current = 1;
            this.loadDocuments();
        },
        showCreateDialog() {
            this.editMode = false;
            this.resetUploadForm();
            this.showUploadDialog = true;
        },
        editDocument(document) {
            this.editMode = true;
            this.uploadForm = {
                id: document.id,
                documentName: document.documentName,
                documentType: document.documentType,
                documentVersion: document.documentVersion,
                effectiveDate: document.effectiveDate,
                status: document.status,
                remark: document.remark,
                fileList: []
            };
            this.showUploadDialog = true;
        },
        closeDialog() {
            this.showUploadDialog = false;
            this.resetUploadForm();
        },
        resetUploadForm() {
            this.editMode = false;
            this.showFileError = false;
            this.uploadForm = {
                id: null,
                documentName: '',
                documentType: '',
                documentVersion: '',
                effectiveDate: '',
                status: 1,
                remark: '',
                fileList: []
            };
            if (this.$refs.uploadForm) {
                this.$refs.uploadForm.resetFields();
            }
        },
        handleFileChange(file, fileList) {
            this.uploadForm.fileList = fileList;
            // 如果有文件了，隐藏错误提示
            if (fileList.length > 0) {
                this.showFileError = false;
            }
        },
        handleFileRemove(file, fileList) {
            this.uploadForm.fileList = fileList;
        },
        async submitDocument() {
            // 先验证基本表单字段
            this.$refs.uploadForm.validate(async (valid) => {
                if (!valid) {
                    console.log('表单验证失败');
                    return;
                }

                // 新增模式下检查文件上传
                if (!this.editMode && this.uploadForm.fileList.length === 0) {
                    this.showFileError = true;
                    this.$message.error('请上传文档文件');
                    return;
                }

                this.uploading = true;
                try {
                    if (this.editMode) {
                        // 编辑模式
                        const response = await axios.put(`/knowledge/${this.uploadForm.id}`, {
                            documentName: this.uploadForm.documentName,
                            documentType: this.uploadForm.documentType,
                            documentVersion: this.uploadForm.documentVersion,
                            effectiveDate: this.uploadForm.effectiveDate,
                            status: this.uploadForm.status,
                            remark: this.uploadForm.remark
                        });
                        
                        if (response.code === 200) {
                            this.$message.success('文档更新成功');
                            this.closeDialog();
                            this.loadDocuments();
                        }
                    } else {
                        // 新增模式
                        const formData = new FormData();
                        formData.append('file', this.uploadForm.fileList[0].raw);
                        formData.append('documentName', this.uploadForm.documentName);
                        formData.append('documentType', this.uploadForm.documentType);
                        formData.append('documentVersion', this.uploadForm.documentVersion);
                        formData.append('effectiveDate', this.uploadForm.effectiveDate);
                        formData.append('remark', this.uploadForm.remark);
                        
                        const response = await axios.post('/knowledge/upload', formData, {
                            headers: { 'Content-Type': 'multipart/form-data' }
                        });
                        
                        if (response.code === 200) {
                            this.$message.success('文档上传成功');
                            this.closeDialog();
                            this.loadDocuments();
                        }
                    }
                } catch (error) {
                    console.error('提交文档失败:', error);
                    this.$message.error('操作失败: ' + (error.response?.data?.message || error.message));
                } finally {
                    this.uploading = false;
                }
            });
        },
        downloadDocument(document) {
            window.open(`/api/uploads/${document.fileUrl}`, '_blank');
        },
        async deleteDocument(documentId) {
            this.$confirm('确认删除此文档？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/knowledge/${documentId}`);
                    if (response.code === 200) {
                        this.$message.success('删除成功');
                        this.loadDocuments();
                    }
                } catch (error) {
                    console.error('删除文档失败:', error);
                    this.$message.error('删除失败');
                }
            });
        },
        getDocumentTypeText(type) {
            const typeObj = this.documentTypes.find(item => item.key === type);
            return typeObj ? typeObj.value : type;
        }
    }
});
