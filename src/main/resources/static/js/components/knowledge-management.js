// 知识库管理组件
Vue.component('knowledge-management', {
    template: `
        <div>
            <el-card>
                <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>知识库文档管理</span>
                    <el-button type="primary" @click="showUploadDialog = true">
                        <i class="el-icon-plus"></i> 添加法规文档
                    </el-button>
                </div>
                
                <!-- 搜索条件 -->
                <div style="margin-bottom: 20px;">
                    <el-form :inline="true">
                        <el-form-item label="文档名称">
                            <el-input v-model="searchForm.documentName" placeholder="请输入文档名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="文档类型">
                            <el-select v-model="searchForm.documentType" placeholder="请选择文档类型" clearable>
                                <el-option v-for="type in documentTypes" :key="type.key" :label="type.value" :value="type.key"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="loadDocuments">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 文档列表 -->
                <el-table :data="documents" v-loading="loading">
                    <el-table-column prop="documentName" label="文档名称"></el-table-column>
                    <el-table-column prop="documentType" label="文档类型">
                        <template slot-scope="scope">
                            {{ getDocumentTypeText(scope.row.documentType) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="documentVersion" label="文档版本"></el-table-column>
                    <el-table-column prop="effectiveDate" label="生效日期"></el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                                {{ scope.row.status === 1 ? '有效' : '无效' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button type="text" @click="editDocument(scope.row)">编辑</el-button>
                            <el-button type="text" @click="downloadDocument(scope.row)">下载</el-button>
                            <el-button type="text" style="color: #f56c6c;" @click="deleteDocument(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="pagination.current"
                        :page-size="pagination.size"
                        :total="pagination.total"
                        layout="total, prev, pager, next">
                    </el-pagination>
                </div>
            </el-card>
            
            <!-- 上传文档对话框 -->
            <el-dialog :title="editMode ? '修改法规文档' : '添加法规文档'" :visible.sync="showUploadDialog" width="600px">
                <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm" label-width="120px">
                    <el-form-item label="文档名称" prop="documentName">
                        <el-input v-model="uploadForm.documentName" placeholder="请输入文档名称"></el-input>
                    </el-form-item>
                    <el-form-item label="文档类型" prop="documentType">
                        <el-select v-model="uploadForm.documentType" placeholder="请选择文档类型">
                            <el-option v-for="type in documentTypes" :key="type.key" :label="type.value" :value="type.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="文档版本">
                        <el-input v-model="uploadForm.documentVersion" placeholder="请输入文档版本"></el-input>
                    </el-form-item>
                    <el-form-item label="生效日期">
                        <el-date-picker
                            v-model="uploadForm.effectiveDate"
                            type="date"
                            placeholder="请选择生效日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="uploadForm.status">
                            <el-radio :label="1">有效</el-radio>
                            <el-radio :label="0">无效</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input type="textarea" v-model="uploadForm.remark" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                    <el-form-item label="文档文件" prop="file" v-if="!editMode">
                        <el-upload
                            ref="fileUpload"
                            :auto-upload="false"
                            :file-list="uploadForm.fileList"
                            :on-change="handleFileChange"
                            :on-remove="handleFileRemove"
                            :limit="1"
                            drag>
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            <div class="el-upload__tip" slot="tip">支持pdf、word格式，文件大小不超过100MB</div>
                        </el-upload>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="showUploadDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitDocument" :loading="uploading">
                        {{ editMode ? '保存' : '上传' }}
                    </el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            uploading: false,
            showUploadDialog: false,
            editMode: false,
            documents: [],
            documentTypes: [],
            searchForm: {
                documentName: '',
                documentType: ''
            },
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            uploadForm: {
                id: null,
                documentName: '',
                documentType: '',
                documentVersion: '',
                effectiveDate: '',
                status: 1,
                remark: '',
                fileList: []
            },
            uploadRules: {
                documentName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
                documentType: [{ required: true, message: '请选择文档类型', trigger: 'change' }],
                file: [{ required: true, message: '请上传文档文件', trigger: 'change' }]
            }
        }
    },
    mounted() {
        this.loadDocuments();
        this.loadDocumentTypes();
    },
    methods: {
        async loadDocuments() {
            this.loading = true;
            try {
                const response = await axios.get('/knowledge/page', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        documentName: this.searchForm.documentName,
                        documentType: this.searchForm.documentType
                    }
                });
                if (response.code === 200) {
                    this.documents = response.data.records;
                    this.pagination.total = response.data.total;
                }
            } catch (error) {
                console.error('加载文档列表失败:', error);
            } finally {
                this.loading = false;
            }
        },
        async loadDocumentTypes() {
            try {
                const response = await axios.get('/dict/type/DOCUMENT_TYPE');
                if (response.code === 200) {
                    this.documentTypes = response.data.map(item => ({
                        key: item.dictKey,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载文档类型失败:', error);
            }
        },
        handlePageChange(page) {
            this.pagination.current = page;
            this.loadDocuments();
        },
        resetSearch() {
            this.searchForm.documentName = '';
            this.searchForm.documentType = '';
            this.pagination.current = 1;
            this.loadDocuments();
        },
        handleFileChange(file, fileList) {
            this.uploadForm.fileList = fileList;
        },
        handleFileRemove(file, fileList) {
            this.uploadForm.fileList = fileList;
        },
        editDocument(document) {
            this.editMode = true;
            this.uploadForm = {
                id: document.id,
                documentName: document.documentName,
                documentType: document.documentType,
                documentVersion: document.documentVersion,
                effectiveDate: document.effectiveDate,
                status: document.status,
                remark: document.remark,
                fileList: []
            };
            this.showUploadDialog = true;
        },
        async submitDocument() {
            // 动态设置验证规则
            const rules = {
                documentName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
                documentType: [{ required: true, message: '请选择文档类型', trigger: 'change' }]
            };

            // 只有在新增模式下才要求上传文件
            if (!this.editMode) {
                rules.file = [{ required: true, message: '请上传文档文件', trigger: 'change' }];
            }

            // 更新验证规则
            this.uploadRules = rules;

            // 等待下一个tick让验证规则生效
            this.$nextTick(() => {
                this.$refs.uploadForm.validate(async (valid) => {
                    if (valid) {
                        if (!this.editMode && this.uploadForm.fileList.length === 0) {
                            this.$message.error('请上传文档文件');
                            return;
                        }

                        this.uploading = true;
                        try {
                        if (this.editMode) {
                            // 编辑模式，只更新文档信息
                            const response = await axios.put(`/knowledge/${this.uploadForm.id}`, {
                                documentName: this.uploadForm.documentName,
                                documentType: this.uploadForm.documentType,
                                documentVersion: this.uploadForm.documentVersion,
                                effectiveDate: this.uploadForm.effectiveDate,
                                status: this.uploadForm.status,
                                remark: this.uploadForm.remark
                            });
                            
                            if (response.code === 200) {
                                this.$message.success('文档更新成功');
                                this.showUploadDialog = false;
                                this.resetUploadForm();
                                this.loadDocuments();
                            }
                        } else {
                            // 新增模式，上传文件
                            const formData = new FormData();
                            formData.append('file', this.uploadForm.fileList[0].raw);
                            formData.append('documentName', this.uploadForm.documentName);
                            formData.append('documentType', this.uploadForm.documentType);
                            formData.append('documentVersion', this.uploadForm.documentVersion);
                            formData.append('effectiveDate', this.uploadForm.effectiveDate);
                            formData.append('remark', this.uploadForm.remark);
                            
                            const response = await axios.post('/knowledge/upload', formData, {
                                headers: { 'Content-Type': 'multipart/form-data' }
                            });
                            
                            if (response.code === 200) {
                                this.$message.success('文档上传成功');
                                this.showUploadDialog = false;
                                this.resetUploadForm();
                                this.loadDocuments();
                            }
                        }
                    } catch (error) {
                        console.error('提交文档失败:', error);
                    } finally {
                        this.uploading = false;
                    }
                });
            });
        },
        resetUploadForm() {
            this.editMode = false;
            this.$refs.uploadForm.resetFields();
            this.uploadForm.fileList = [];

            // 重置验证规则为默认状态（新增模式）
            this.uploadRules = {
                documentName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
                documentType: [{ required: true, message: '请选择文档类型', trigger: 'change' }],
                file: [{ required: true, message: '请上传文档文件', trigger: 'change' }]
            };
        },
        downloadDocument(document) {
            // 模拟下载功能
            window.open(`/api/uploads/${document.fileUrl}`, '_blank');
        },
        async deleteDocument(documentId) {
            this.$confirm('确认删除此文档？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/knowledge/${documentId}`);
                    if (response.code === 200) {
                        this.$message.success('删除成功');
                        this.loadDocuments();
                    }
                } catch (error) {
                    console.error('删除文档失败:', error);
                }
            });
        },
        getDocumentTypeText(type) {
            const typeObj = this.documentTypes.find(item => item.key === type);
            return typeObj ? typeObj.value : type;
        }
    }
});
