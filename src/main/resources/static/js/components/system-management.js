// 系统管理组件
Vue.component('system-management', {
    template: `
        <div>
            <el-card>
                <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>字典管理</span>
                    <el-button type="primary" @click="showCreateDialog = true">
                        <i class="el-icon-plus"></i> 新增字典
                    </el-button>
                </div>
                
                <!-- 字典类型选择 -->
                <div style="margin-bottom: 20px;">
                    <el-form :inline="true">
                        <el-form-item label="字典类型">
                            <el-select v-model="selectedDictType" placeholder="请选择字典类型" @change="loadDictItems">
                                <el-option v-for="type in dictTypes" :key="type" :label="type" :value="type"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="loadDictItems">查询</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 字典列表 -->
                <el-table :data="dictItems" v-loading="loading">
                    <el-table-column prop="dictType" label="字典类型"></el-table-column>
                    <el-table-column prop="dictKey" label="字典键"></el-table-column>
                    <el-table-column prop="dictValue" label="字典值"></el-table-column>
                    <el-table-column prop="sortOrder" label="排序"></el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                                {{ scope.row.status === 1 ? '启用' : '禁用' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注"></el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作" width="150">
                        <template slot-scope="scope">
                            <el-button type="text" @click="editDict(scope.row)">编辑</el-button>
                            <el-button type="text" style="color: #f56c6c;" @click="deleteDict(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="pagination.current"
                        :page-size="pagination.size"
                        :total="pagination.total"
                        layout="total, prev, pager, next">
                    </el-pagination>
                </div>
            </el-card>
            
            <!-- 新增/编辑字典对话框 -->
            <el-dialog :title="editMode ? '编辑字典' : '新增字典'" :visible.sync="showCreateDialog" width="500px">
                <el-form :model="dictForm" :rules="dictRules" ref="dictForm" label-width="100px">
                    <el-form-item label="字典类型" prop="dictType">
                        <el-input v-model="dictForm.dictType" placeholder="请输入字典类型" :disabled="editMode"></el-input>
                    </el-form-item>
                    <el-form-item label="字典键" prop="dictKey">
                        <el-input v-model="dictForm.dictKey" placeholder="请输入字典键" :disabled="editMode"></el-input>
                    </el-form-item>
                    <el-form-item label="字典值" prop="dictValue">
                        <el-input v-model="dictForm.dictValue" placeholder="请输入字典值"></el-input>
                    </el-form-item>
                    <el-form-item label="排序" prop="sortOrder">
                        <el-input-number v-model="dictForm.sortOrder" :min="0" placeholder="请输入排序"></el-input-number>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="dictForm.status">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="0">禁用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input type="textarea" v-model="dictForm.remark" placeholder="请输入备注"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="showCreateDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitDict" :loading="submitting">
                        {{ editMode ? '保存' : '新增' }}
                    </el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            submitting: false,
            showCreateDialog: false,
            editMode: false,
            dictTypes: [],
            selectedDictType: '',
            dictItems: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            dictForm: {
                id: null,
                dictType: '',
                dictKey: '',
                dictValue: '',
                sortOrder: 0,
                status: 1,
                remark: ''
            },
            dictRules: {
                dictType: [{ required: true, message: '请输入字典类型', trigger: 'blur' }],
                dictKey: [{ required: true, message: '请输入字典键', trigger: 'blur' }],
                dictValue: [{ required: true, message: '请输入字典值', trigger: 'blur' }],
                sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }]
            }
        }
    },
    mounted() {
        this.loadDictTypes();
        this.loadAllDictItems();
    },
    methods: {
        async loadDictTypes() {
            try {
                const response = await axios.get('/dict/types');
                if (response.code === 200) {
                    this.dictTypes = response.data;
                }
            } catch (error) {
                console.error('加载字典类型失败:', error);
            }
        },
        async loadAllDictItems() {
            this.loading = true;
            try {
                const response = await axios.get('/dict/page', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size
                    }
                });
                if (response.code === 200) {
                    this.dictItems = response.data.records;
                    this.pagination.total = response.data.total;
                }
            } catch (error) {
                console.error('加载字典列表失败:', error);
            } finally {
                this.loading = false;
            }
        },
        async loadDictItems() {
            if (!this.selectedDictType) {
                this.loadAllDictItems();
                return;
            }
            
            this.loading = true;
            try {
                const response = await axios.get(`/dict/type/${this.selectedDictType}`);
                if (response.code === 200) {
                    this.dictItems = response.data;
                    this.pagination.total = response.data.length;
                }
            } catch (error) {
                console.error('加载字典项失败:', error);
            } finally {
                this.loading = false;
            }
        },
        handlePageChange(page) {
            this.pagination.current = page;
            if (this.selectedDictType) {
                this.loadDictItems();
            } else {
                this.loadAllDictItems();
            }
        },
        editDict(dict) {
            this.editMode = true;
            this.dictForm = {
                id: dict.id,
                dictType: dict.dictType,
                dictKey: dict.dictKey,
                dictValue: dict.dictValue,
                sortOrder: dict.sortOrder,
                status: dict.status,
                remark: dict.remark
            };
            this.showCreateDialog = true;
        },
        async submitDict() {
            this.$refs.dictForm.validate(async (valid) => {
                if (valid) {
                    this.submitting = true;
                    try {
                        let response;
                        if (this.editMode) {
                            response = await axios.put(`/dict/${this.dictForm.id}`, this.dictForm);
                        } else {
                            response = await axios.post('/dict', this.dictForm);
                        }
                        
                        if (response.code === 200) {
                            this.$message.success(this.editMode ? '字典更新成功' : '字典创建成功');
                            this.showCreateDialog = false;
                            this.resetDictForm();
                            this.loadDictTypes();
                            if (this.selectedDictType) {
                                this.loadDictItems();
                            } else {
                                this.loadAllDictItems();
                            }
                        }
                    } catch (error) {
                        console.error('提交字典失败:', error);
                    } finally {
                        this.submitting = false;
                    }
                }
            });
        },
        resetDictForm() {
            this.editMode = false;
            this.$refs.dictForm.resetFields();
            this.dictForm = {
                id: null,
                dictType: '',
                dictKey: '',
                dictValue: '',
                sortOrder: 0,
                status: 1,
                remark: ''
            };
        },
        async deleteDict(dictId) {
            this.$confirm('确认删除此字典项？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/dict/${dictId}`);
                    if (response.code === 200) {
                        this.$message.success('删除成功');
                        this.loadDictTypes();
                        if (this.selectedDictType) {
                            this.loadDictItems();
                        } else {
                            this.loadAllDictItems();
                        }
                    }
                } catch (error) {
                    console.error('删除字典失败:', error);
                }
            });
        }
    }
});
