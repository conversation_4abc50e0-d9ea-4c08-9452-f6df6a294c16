// 简化版知识库管理组件（用于测试）
Vue.component('knowledge-management-simple', {
    template: `
        <div>
            <el-card>
                <div slot="header">
                    <span>知识库文档管理（简化版）</span>
                </div>
                
                <p>组件加载成功！</p>
                <p>API状态：{{ apiStatus }}</p>
                <p>文档数量：{{ documents.length }}</p>
                
                <el-button type="primary" @click="testApi">测试API</el-button>
                <el-button type="success" @click="showDialog = true">测试对话框</el-button>
                
                <el-table :data="documents" style="margin-top: 20px;">
                    <el-table-column prop="documentName" label="文档名称"></el-table-column>
                    <el-table-column prop="documentType" label="文档类型"></el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                </el-table>
            </el-card>
            
            <el-dialog title="测试对话框" :visible.sync="showDialog" width="500px">
                <p>这是一个测试对话框，用于验证组件功能。</p>
                <div slot="footer">
                    <el-button @click="showDialog = false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            apiStatus: '未测试',
            documents: [],
            showDialog: false
        }
    },
    mounted() {
        this.loadDocuments();
    },
    methods: {
        async testApi() {
            try {
                const response = await axios.get('/knowledge/page?pageNum=1&pageSize=5');
                if (response.code === 200) {
                    this.apiStatus = 'API正常';
                    this.$message.success('API测试成功');
                } else {
                    this.apiStatus = 'API异常';
                    this.$message.error('API测试失败');
                }
            } catch (error) {
                this.apiStatus = 'API错误: ' + error.message;
                this.$message.error('API测试失败: ' + error.message);
            }
        },
        async loadDocuments() {
            try {
                const response = await axios.get('/knowledge/page?pageNum=1&pageSize=10');
                if (response.code === 200) {
                    this.documents = response.data.records || [];
                    this.apiStatus = 'API正常，文档数量: ' + this.documents.length;
                }
            } catch (error) {
                this.apiStatus = 'API加载失败: ' + error.message;
                console.error('加载文档失败:', error);
            }
        }
    }
});
