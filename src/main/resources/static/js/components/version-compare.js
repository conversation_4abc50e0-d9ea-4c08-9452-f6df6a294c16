// 版本差异对比组件
Vue.component('version-compare', {
    template: `
        <div>
            <el-row :gutter="20">
                <!-- 图片对比 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header">
                            <span>图片对比</span>
                        </div>
                        <el-form :model="imageCompareForm" ref="imageCompareForm" label-width="100px">
                            <el-form-item label="任务名称" prop="taskName">
                                <el-input v-model="imageCompareForm.taskName" placeholder="请输入任务名称"></el-input>
                            </el-form-item>
                            <el-form-item label="图片1" prop="file1">
                                <el-upload
                                    ref="imageUpload1"
                                    :auto-upload="false"
                                    :file-list="imageCompareForm.file1List"
                                    :on-change="(file, fileList) => handleImageFileChange(file, fileList, 'file1List')"
                                    :on-remove="(file, fileList) => handleImageFileRemove(file, fileList, 'file1List')"
                                    :limit="1"
                                    accept="image/*"
                                    drag>
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">支持jpg、png、gif格式</div>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="图片2" prop="file2">
                                <el-upload
                                    ref="imageUpload2"
                                    :auto-upload="false"
                                    :file-list="imageCompareForm.file2List"
                                    :on-change="(file, fileList) => handleImageFileChange(file, fileList, 'file2List')"
                                    :on-remove="(file, fileList) => handleImageFileRemove(file, fileList, 'file2List')"
                                    :limit="1"
                                    accept="image/*"
                                    drag>
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">支持jpg、png、gif格式</div>
                                </el-upload>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="submitImageCompare" :loading="imageComparing">
                                    提交对比
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </el-col>
                
                <!-- 图文对比 -->
                <el-col :span="12">
                    <el-card>
                        <div slot="header">
                            <span>图文对比</span>
                        </div>
                        <el-form :model="textImageCompareForm" ref="textImageCompareForm" label-width="100px">
                            <el-form-item label="任务名称" prop="taskName">
                                <el-input v-model="textImageCompareForm.taskName" placeholder="请输入任务名称"></el-input>
                            </el-form-item>
                            <el-form-item label="文档文件" prop="textFile">
                                <el-upload
                                    ref="textUpload"
                                    :auto-upload="false"
                                    :file-list="textImageCompareForm.textFileList"
                                    :on-change="(file, fileList) => handleTextImageFileChange(file, fileList, 'textFileList')"
                                    :on-remove="(file, fileList) => handleTextImageFileRemove(file, fileList, 'textFileList')"
                                    :limit="1"
                                    drag>
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将文档拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">支持pdf、word格式</div>
                                </el-upload>
                            </el-form-item>
                            <el-form-item label="图片文件" prop="imageFile">
                                <el-upload
                                    ref="imageUpload"
                                    :auto-upload="false"
                                    :file-list="textImageCompareForm.imageFileList"
                                    :on-change="(file, fileList) => handleTextImageFileChange(file, fileList, 'imageFileList')"
                                    :on-remove="(file, fileList) => handleTextImageFileRemove(file, fileList, 'imageFileList')"
                                    :limit="1"
                                    accept="image/*"
                                    drag>
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">支持jpg、png、gif格式</div>
                                </el-upload>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="submitTextImageCompare" :loading="textImageComparing">
                                    提交对比
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </el-col>
            </el-row>
            
            <!-- 对比任务列表 -->
            <el-card style="margin-top: 20px;">
                <div slot="header">
                    <span>对比任务列表</span>
                </div>
                
                <!-- 搜索条件 -->
                <div style="margin-bottom: 20px;">
                    <el-form :inline="true">
                        <el-form-item label="任务类型">
                            <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" clearable>
                                <el-option label="图片对比" value="IMAGE_COMPARE"></el-option>
                                <el-option label="图文对比" value="TEXT_IMAGE_COMPARE"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="任务状态">
                            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                                <el-option label="待对比" value="PENDING"></el-option>
                                <el-option label="对比中" value="PROCESSING"></el-option>
                                <el-option label="已完成" value="COMPLETED"></el-option>
                                <el-option label="失败" value="FAILED"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="loadTasks">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 任务列表 -->
                <el-table :data="tasks" v-loading="loading">
                    <el-table-column prop="taskName" label="任务名称"></el-table-column>
                    <el-table-column prop="taskType" label="任务类型">
                        <template slot-scope="scope">
                            <el-tag :type="getTaskTypeColor(scope.row.taskType)">
                                {{ getTaskTypeText(scope.row.taskType) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="progress" label="进度">
                        <template slot-scope="scope">
                            <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row.status)"></el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button 
                                v-if="scope.row.status === 'COMPLETED'" 
                                type="text" 
                                @click="viewResult(scope.row)">
                                查看结果
                            </el-button>
                            <el-button type="text" @click="deleteTask(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="pagination.current"
                        :page-size="pagination.size"
                        :total="pagination.total"
                        layout="total, prev, pager, next">
                    </el-pagination>
                </div>
            </el-card>
            
            <!-- 对比结果对话框 -->
            <el-dialog title="对比结果" :visible.sync="showResultDialog" width="800px">
                <div v-if="currentResult">
                    <pre style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 4px;">{{ currentResult.resultData }}</pre>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            imageComparing: false,
            textImageComparing: false,
            showResultDialog: false,
            tasks: [],
            currentResult: null,
            searchForm: {
                taskType: '',
                status: ''
            },
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            imageCompareForm: {
                taskName: '',
                file1List: [],
                file2List: []
            },
            textImageCompareForm: {
                taskName: '',
                textFileList: [],
                imageFileList: []
            }
        }
    },
    mounted() {
        this.loadTasks();
    },
    methods: {
        async loadTasks() {
            this.loading = true;
            try {
                const response = await axios.get('/task/page', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        taskType: this.searchForm.taskType,
                        status: this.searchForm.status
                    }
                });
                if (response.code === 200) {
                    // 过滤出对比任务
                    this.tasks = response.data.records.filter(task => 
                        task.taskType === 'IMAGE_COMPARE' || task.taskType === 'TEXT_IMAGE_COMPARE'
                    );
                    this.pagination.total = this.tasks.length;
                }
            } catch (error) {
                console.error('加载任务列表失败:', error);
            } finally {
                this.loading = false;
            }
        },
        handlePageChange(page) {
            this.pagination.current = page;
            this.loadTasks();
        },
        resetSearch() {
            this.searchForm.taskType = '';
            this.searchForm.status = '';
            this.pagination.current = 1;
            this.loadTasks();
        },
        handleImageFileChange(file, fileList, listName) {
            this.imageCompareForm[listName] = fileList;
        },
        handleImageFileRemove(file, fileList, listName) {
            this.imageCompareForm[listName] = fileList;
        },
        handleTextImageFileChange(file, fileList, listName) {
            this.textImageCompareForm[listName] = fileList;
        },
        handleTextImageFileRemove(file, fileList, listName) {
            this.textImageCompareForm[listName] = fileList;
        },
        async submitImageCompare() {
            if (!this.imageCompareForm.taskName) {
                this.$message.error('请输入任务名称');
                return;
            }
            if (this.imageCompareForm.file1List.length === 0 || this.imageCompareForm.file2List.length === 0) {
                this.$message.error('请上传两张图片');
                return;
            }
            
            this.imageComparing = true;
            try {
                const formData = new FormData();
                formData.append('taskName', this.imageCompareForm.taskName);
                formData.append('file1', this.imageCompareForm.file1List[0].raw);
                formData.append('file2', this.imageCompareForm.file2List[0].raw);
                
                const response = await axios.post('/task/image-compare', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });
                
                if (response.code === 200) {
                    this.$message.success('图片对比任务创建成功');
                    this.resetImageCompareForm();
                    this.loadTasks();
                }
            } catch (error) {
                console.error('创建图片对比任务失败:', error);
            } finally {
                this.imageComparing = false;
            }
        },
        async submitTextImageCompare() {
            if (!this.textImageCompareForm.taskName) {
                this.$message.error('请输入任务名称');
                return;
            }
            if (this.textImageCompareForm.textFileList.length === 0 || this.textImageCompareForm.imageFileList.length === 0) {
                this.$message.error('请上传文档和图片文件');
                return;
            }
            
            this.textImageComparing = true;
            try {
                const formData = new FormData();
                formData.append('taskName', this.textImageCompareForm.taskName);
                formData.append('textFile', this.textImageCompareForm.textFileList[0].raw);
                formData.append('imageFile', this.textImageCompareForm.imageFileList[0].raw);
                
                const response = await axios.post('/task/text-image-compare', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });
                
                if (response.code === 200) {
                    this.$message.success('图文对比任务创建成功');
                    this.resetTextImageCompareForm();
                    this.loadTasks();
                }
            } catch (error) {
                console.error('创建图文对比任务失败:', error);
            } finally {
                this.textImageComparing = false;
            }
        },
        resetImageCompareForm() {
            this.imageCompareForm.taskName = '';
            this.imageCompareForm.file1List = [];
            this.imageCompareForm.file2List = [];
        },
        resetTextImageCompareForm() {
            this.textImageCompareForm.taskName = '';
            this.textImageCompareForm.textFileList = [];
            this.textImageCompareForm.imageFileList = [];
        },
        async viewResult(task) {
            try {
                const response = await axios.get(`/task/${task.id}/report`);
                if (response.code === 200) {
                    this.currentResult = response.data;
                    this.showResultDialog = true;
                }
            } catch (error) {
                this.$message.error('获取对比结果失败');
            }
        },
        async deleteTask(taskId) {
            this.$confirm('确认删除此任务？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/task/${taskId}`);
                    if (response.code === 200) {
                        this.$message.success('删除成功');
                        this.loadTasks();
                    }
                } catch (error) {
                    console.error('删除任务失败:', error);
                }
            });
        },
        getTaskTypeColor(type) {
            return type === 'IMAGE_COMPARE' ? 'primary' : 'success';
        },
        getTaskTypeText(type) {
            const texts = {
                'IMAGE_COMPARE': '图片对比',
                'TEXT_IMAGE_COMPARE': '图文对比'
            };
            return texts[type] || type;
        },
        getStatusType(status) {
            const types = {
                'PENDING': 'info',
                'PROCESSING': 'warning',
                'COMPLETED': 'success',
                'FAILED': 'danger'
            };
            return types[status] || 'info';
        },
        getStatusText(status) {
            const texts = {
                'PENDING': '待对比',
                'PROCESSING': '对比中',
                'COMPLETED': '已完成',
                'FAILED': '失败'
            };
            return texts[status] || status;
        },
        getProgressStatus(status) {
            return status === 'COMPLETED' ? 'success' : (status === 'FAILED' ? 'exception' : null);
        }
    }
});
