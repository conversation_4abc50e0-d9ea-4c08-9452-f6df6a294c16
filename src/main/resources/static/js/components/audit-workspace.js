// 审核工作台组件
Vue.component('audit-workspace', {
    template: `
        <div>
            <el-card>
                <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>审核任务列表</span>
                    <el-button type="primary" @click="showCreateDialog = true">
                        <i class="el-icon-plus"></i> 发起审核任务
                    </el-button>
                </div>
                
                <!-- 搜索条件 -->
                <div style="margin-bottom: 20px;">
                    <el-form :inline="true">
                        <el-form-item label="任务状态">
                            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                                <el-option label="待审核" value="PENDING"></el-option>
                                <el-option label="审核中" value="PROCESSING"></el-option>
                                <el-option label="已完成" value="COMPLETED"></el-option>
                                <el-option label="审核失败" value="FAILED"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="loadTasks">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 任务列表 -->
                <el-table :data="tasks" v-loading="loading">
                    <el-table-column prop="taskName" label="任务名称"></el-table-column>
                    <el-table-column prop="brand" label="品牌"></el-table-column>
                    <el-table-column prop="status" label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="progress" label="进度">
                        <template slot-scope="scope">
                            <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row.status)"></el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button 
                                v-if="scope.row.status === 'COMPLETED'" 
                                type="text" 
                                @click="viewReport(scope.row)">
                                查看报告
                            </el-button>
                            <el-button type="text" @click="deleteTask(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="pagination.current"
                        :page-size="pagination.size"
                        :total="pagination.total"
                        layout="total, prev, pager, next">
                    </el-pagination>
                </div>
            </el-card>
            
            <!-- 创建任务对话框 -->
            <el-dialog title="发起审核任务" :visible.sync="showCreateDialog" width="600px">
                <el-form :model="createForm" :rules="createRules" ref="createForm" label-width="120px">
                    <el-form-item label="审核名称" prop="taskName">
                        <el-input v-model="createForm.taskName" placeholder="请输入审核名称"></el-input>
                    </el-form-item>
                    <el-form-item label="品牌" prop="brand">
                        <el-select v-model="createForm.brand" placeholder="请选择品牌">
                            <el-option v-for="brand in brands" :key="brand.key" :label="brand.value" :value="brand.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="法规文件" prop="regulationFileIds">
                        <el-select v-model="createForm.regulationFileIds" multiple placeholder="请选择法规文件">
                            <el-option v-for="doc in knowledgeDocs" :key="doc.id" :label="doc.documentName" :value="doc.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="文件版本">
                        <el-input v-model="createForm.fileVersion" placeholder="请输入文件版本"></el-input>
                    </el-form-item>
                    <el-form-item label="忽略描述信息">
                        <el-switch v-model="createForm.ignoreDescription"></el-switch>
                    </el-form-item>
                    <el-form-item label="审核文件" prop="auditFiles">
                        <el-upload
                            ref="upload"
                            :auto-upload="false"
                            :file-list="createForm.auditFiles"
                            :on-change="handleFileChange"
                            :on-remove="handleFileRemove"
                            multiple
                            drag>
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            <div class="el-upload__tip" slot="tip">支持pdf、word、excel、ppt及图片格式</div>
                        </el-upload>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="showCreateDialog = false">取消</el-button>
                    <el-button type="primary" @click="createTask" :loading="creating">提交</el-button>
                </div>
            </el-dialog>
            
            <!-- 报告查看对话框 -->
            <el-dialog title="审核报告" :visible.sync="showReportDialog" width="800px">
                <div v-if="currentReport">
                    <el-table :data="reportItems">
                        <el-table-column prop="riskLevel" label="风险等级" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
                                    {{ getRiskLevelText(scope.row.riskLevel) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="problemDescription" label="问题描述"></el-table-column>
                        <el-table-column prop="referenceRegulation" label="参考法规"></el-table-column>
                        <el-table-column prop="modificationSuggestion" label="修改建议"></el-table-column>
                    </el-table>
                </div>
            </el-dialog>
        </div>
    `,
    data() {
        return {
            loading: false,
            creating: false,
            showCreateDialog: false,
            showReportDialog: false,
            tasks: [],
            brands: [],
            knowledgeDocs: [],
            currentReport: null,
            reportItems: [],
            searchForm: {
                status: ''
            },
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            createForm: {
                taskName: '',
                brand: '',
                regulationFileIds: [],
                fileVersion: '',
                ignoreDescription: false,
                auditFiles: []
            },
            createRules: {
                taskName: [{ required: true, message: '请输入审核名称', trigger: 'blur' }],
                brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
                regulationFileIds: [{ required: true, message: '请选择法规文件', trigger: 'change' }],
                auditFiles: [{ required: true, message: '请上传审核文件', trigger: 'change' }]
            }
        }
    },
    mounted() {
        this.loadTasks();
        this.loadBrands();
        this.loadKnowledgeDocs();
    },
    methods: {
        async loadTasks() {
            this.loading = true;
            try {
                const response = await axios.get('/task/page', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        taskType: 'AUDIT',
                        status: this.searchForm.status
                    }
                });
                if (response.code === 200) {
                    this.tasks = response.data.records;
                    this.pagination.total = response.data.total;
                }
            } catch (error) {
                console.error('加载任务列表失败:', error);
            } finally {
                this.loading = false;
            }
        },
        async loadBrands() {
            try {
                const response = await axios.get('/dict/type/BRAND');
                if (response.code === 200) {
                    this.brands = response.data.map(item => ({
                        key: item.dictKey,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载品牌列表失败:', error);
            }
        },
        async loadKnowledgeDocs() {
            try {
                const response = await axios.get('/knowledge/page', {
                    params: { pageNum: 1, pageSize: 100 }
                });
                if (response.code === 200) {
                    this.knowledgeDocs = response.data.records;
                }
            } catch (error) {
                console.error('加载知识库文档失败:', error);
            }
        },
        handlePageChange(page) {
            this.pagination.current = page;
            this.loadTasks();
        },
        resetSearch() {
            this.searchForm.status = '';
            this.pagination.current = 1;
            this.loadTasks();
        },
        handleFileChange(file, fileList) {
            this.createForm.auditFiles = fileList;
        },
        handleFileRemove(file, fileList) {
            this.createForm.auditFiles = fileList;
        },
        async createTask() {
            this.$refs.createForm.validate(async (valid) => {
                if (valid) {
                    this.creating = true;
                    try {
                        const formData = new FormData();
                        formData.append('taskName', this.createForm.taskName);
                        formData.append('brand', this.createForm.brand);
                        formData.append('regulationFileIds', JSON.stringify(this.createForm.regulationFileIds));
                        formData.append('fileVersion', this.createForm.fileVersion);
                        formData.append('ignoreDescription', this.createForm.ignoreDescription);
                        
                        this.createForm.auditFiles.forEach(file => {
                            formData.append('auditFiles', file.raw);
                        });
                        
                        const response = await axios.post('/task/audit', formData, {
                            headers: { 'Content-Type': 'multipart/form-data' }
                        });
                        
                        if (response.code === 200) {
                            this.$message.success('任务创建成功');
                            this.showCreateDialog = false;
                            this.resetCreateForm();
                            this.loadTasks();
                        }
                    } catch (error) {
                        console.error('创建任务失败:', error);
                    } finally {
                        this.creating = false;
                    }
                }
            });
        },
        resetCreateForm() {
            this.$refs.createForm.resetFields();
            this.createForm.auditFiles = [];
        },
        async viewReport(task) {
            try {
                const response = await axios.get(`/task/${task.id}/report`);
                if (response.code === 200) {
                    this.currentReport = response.data;
                    this.reportItems = JSON.parse(response.data.resultData);
                    this.showReportDialog = true;
                }
            } catch (error) {
                this.$message.error('获取报告失败');
            }
        },
        async deleteTask(taskId) {
            this.$confirm('确认删除此任务？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/task/${taskId}`);
                    if (response.code === 200) {
                        this.$message.success('删除成功');
                        this.loadTasks();
                    }
                } catch (error) {
                    console.error('删除任务失败:', error);
                }
            });
        },
        getStatusType(status) {
            const types = {
                'PENDING': 'info',
                'PROCESSING': 'warning',
                'COMPLETED': 'success',
                'FAILED': 'danger'
            };
            return types[status] || 'info';
        },
        getStatusText(status) {
            const texts = {
                'PENDING': '待审核',
                'PROCESSING': '审核中',
                'COMPLETED': '已完成',
                'FAILED': '审核失败'
            };
            return texts[status] || status;
        },
        getProgressStatus(status) {
            return status === 'COMPLETED' ? 'success' : (status === 'FAILED' ? 'exception' : null);
        },
        getRiskLevelType(level) {
            const types = {
                'HIGH': 'danger',
                'MEDIUM': 'warning',
                'LOW': 'success'
            };
            return types[level] || 'info';
        },
        getRiskLevelText(level) {
            const texts = {
                'HIGH': '高',
                'MEDIUM': '中',
                'LOW': '低'
            };
            return texts[level] || level;
        }
    }
});
