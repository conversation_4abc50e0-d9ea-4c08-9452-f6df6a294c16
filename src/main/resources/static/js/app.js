// 主应用
new Vue({
    el: '#app',
    data() {
        return {
            activeMenu: 'audit',
            sidebarCollapsed: false,
            storageInfo: {
                storageType: 'LOCAL',
                description: '文件将保存到本地存储'
            }
        }
    },
    methods: {
        handleMenuSelect(key) {
            this.activeMenu = key;
        },
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
        }
    },
    mounted() {
        // 设置axios默认配置
        axios.defaults.baseURL = '/api';
        axios.defaults.timeout = 30000;

        // 请求拦截器
        axios.interceptors.request.use(
            config => {
                // 可以在这里添加token等
                return config;
            },
            error => {
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        axios.interceptors.response.use(
            response => {
                return response.data;
            },
            error => {
                this.$message.error('请求失败: ' + (error.response?.data?.message || error.message));
                return Promise.reject(error);
            }
        );

        // 获取存储配置信息
        this.loadStorageConfig();
    },
    methods: {
        handleMenuSelect(key) {
            this.activeMenu = key;
        },
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
        },
        async loadStorageConfig() {
            try {
                const response = await axios.get('/file/config');
                if (response.code === 200) {
                    this.storageInfo = response.data;
                }
            } catch (error) {
                console.error('获取存储配置失败:', error);
            }
        }
    }
});
