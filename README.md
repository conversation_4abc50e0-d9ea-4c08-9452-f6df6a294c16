# 元数据管理系统

一个现代化的元数据管理系统，用于管理多平台数据采集和标准化的元数据信息。

## 系统架构

- **后端**: Spring Boot + MyBatis Plus + MySQL
- **前端**: Vue 3 + Element Plus + Vite
- **风格**: 现代简约设计

## 功能特性

### 核心功能
1. **平台管理** - 管理支持的电商平台信息
2. **数据类型管理** - 管理订单、退单、商品、类目等数据类型
3. **标准字段管理** - 定义统一的标准字段规范
4. **字段映射管理** - 管理不同平台字段到标准字段的映射关系
5. **查询中心** - 提供平台能力查询和字段对比功能

### 解决的问题
- ✅ 外部经常询问支持哪些平台的什么数据
- ✅ 数据标准化的规则管理
- ✅ 不同渠道对于同一个标准化字段的定义区别

## 快速开始

### 环境要求
- Java 8+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 1. 数据库初始化

```bash
# 创建数据库并导入表结构
mysql -u root -p < sql/metadata_system.sql
```

### 2. 后端启动

```bash
# 修改数据库连接配置
# 编辑 src/main/resources/application.yml
# 更新数据库连接信息：url、username、password

# 启动后端服务
mvn spring-boot:run

# 或者编译后运行
mvn clean package
java -jar target/aitest-project-1.0-SNAPSHOT.jar
```

后端服务将在 http://localhost:8080/api 启动

### 3. 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 http://localhost:3000 启动

## 项目结构

```
├── src/main/java/com/jcoder/metadata/     # 后端源码
│   ├── entity/                            # 实体类
│   ├── mapper/                            # 数据访问层
│   ├── service/                           # 业务逻辑层
│   ├── controller/                        # 控制器层
│   ├── config/                            # 配置类
│   └── common/                            # 通用类
├── src/main/resources/                    # 后端资源文件
├── frontend/                              # 前端项目
│   ├── src/
│   │   ├── views/                         # 页面组件
│   │   ├── components/                    # 通用组件
│   │   ├── api/                           # API接口
│   │   ├── utils/                         # 工具函数
│   │   └── styles/                        # 样式文件
│   └── package.json
├── sql/                                   # 数据库脚本
└── logs/                                  # 日志文件
```

## 数据库设计

### 核心表结构
- `platform` - 平台信息表
- `data_type` - 数据类型表
- `standard_field` - 标准字段定义表
- `platform_field_mapping` - 平台字段映射表
- `data_dictionary` - 数据字典表
- `platform_data_support` - 平台数据支持情况表

## API接口

### 平台管理
- `GET /api/platform/page` - 分页查询平台列表
- `GET /api/platform/list` - 查询所有启用平台
- `GET /api/platform/{id}` - 根据ID查询平台详情
- `POST /api/platform` - 创建平台
- `PUT /api/platform/{id}` - 更新平台
- `DELETE /api/platform/{id}` - 删除平台
- `PUT /api/platform/{id}/status` - 更新平台状态

## 开发计划

### 已完成 ✅
- [x] 数据库设计和表结构创建
- [x] 后端基础架构搭建
- [x] 平台管理功能完整实现
- [x] 前端基础框架和布局
- [x] 现代简约UI设计
- [x] 响应式设计支持

### 开发中 🚧
- [ ] 数据类型管理功能
- [ ] 标准字段管理功能
- [ ] 字段映射管理功能
- [ ] 查询中心功能

### 计划中 📋
- [ ] 数据导入导出功能
- [ ] API文档自动生成
- [ ] 数据质量监控
- [ ] 权限管理系统
- [ ] 操作日志记录

## 技术特点

### 后端特性
- 使用MyBatis Plus简化数据访问
- 统一的响应结果封装
- 完善的异常处理机制
- 支持跨域访问
- JSON类型字段支持

### 前端特性
- Vue 3 Composition API
- Element Plus组件库
- 响应式设计
- 现代简约UI风格
- 组件化开发
- 路由懒加载

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至项目维护者

---

**元数据管理系统** - 让数据标准化更简单 🚀
