# 元数据管理系统部署指南

## 系统概述

元数据管理系统是一个现代化的前后端分离应用，用于管理多平台数据采集和标准化的元数据信息。

### 技术栈
- **后端**: Spring Boot 2.7.18 + MyBatis Plus 3.4.3 + MySQL 8.0
- **前端**: Vue 3 + Element Plus + Vite
- **数据库**: MySQL 8.0+

## 部署步骤

### 1. 环境准备

确保以下软件已安装：
- Java 8 或更高版本
- Maven 3.6+
- Node.js 16+
- MySQL 8.0+

### 2. 数据库配置

#### 2.1 创建数据库
```sql
CREATE DATABASE metadata_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.2 导入表结构和初始数据
```bash
mysql -u root -p metadata_system < sql/metadata_system.sql
```

#### 2.3 验证数据库
登录MySQL确认表已创建：
```sql
USE metadata_system;
SHOW TABLES;
SELECT * FROM platform;
```

### 3. 后端部署

#### 3.1 配置数据库连接
编辑 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: **********************************************************************************************************************
    username: your_username
    password: your_password
```

#### 3.2 启动后端服务

**开发环境**：
```bash
# 方式1：使用Maven插件
mvn spring-boot:run

# 方式2：使用批处理文件
start-backend.bat
```

**生产环境**：
```bash
# 编译打包
mvn clean package -DskipTests

# 运行JAR包
java -jar target/aitest-project-1.0-SNAPSHOT.jar

# 或者指定配置文件
java -jar target/aitest-project-1.0-SNAPSHOT.jar --spring.config.location=application-prod.yml
```

#### 3.3 验证后端服务
访问：http://localhost:8080/api/platform/list

### 4. 前端部署

#### 4.1 安装依赖
```bash
cd frontend
npm install
```

#### 4.2 启动前端服务

**开发环境**：
```bash
# 启动开发服务器
npm run dev

# 或使用批处理文件
../start-frontend.bat
```

**生产环境**：
```bash
# 构建生产版本
npm run build

# 部署到Web服务器
# 将 dist 目录内容复制到 Nginx/Apache 等Web服务器
```

#### 4.3 验证前端应用
访问：http://localhost:3000

## 配置说明

### 后端配置

#### application.yml 主要配置项：
```yaml
server:
  port: 8080                    # 服务端口
  servlet:
    context-path: /api          # 上下文路径

spring:
  datasource:
    url: *******************************************
    username: root
    password: 123456
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.jcoder: debug
  file:
    path: logs/
    name: logs/metadata-system.log
```

### 前端配置

#### vite.config.js 主要配置：
```javascript
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 生产环境部署

### 1. 使用Docker部署

#### 1.1 创建Dockerfile（后端）
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/aitest-project-1.0-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 1.2 创建docker-compose.yml
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: metadata_system
    ports:
      - "3306:3306"
    volumes:
      - ./sql/metadata_system.sql:/docker-entrypoint-initdb.d/init.sql

  backend:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      SPRING_DATASOURCE_URL: ***************************************

  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html
```

### 2. 使用Nginx部署前端

#### nginx.conf 配置示例：
```nginx
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://backend:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 监控和维护

### 1. 日志管理
- 后端日志：`logs/metadata-system.log`
- 前端访问日志：通过Web服务器配置

### 2. 健康检查
- 后端健康检查：`GET /api/platform/list`
- 数据库连接检查：查看应用日志

### 3. 性能监控
- JVM监控：使用JVisualVM或其他APM工具
- 数据库监控：MySQL性能监控
- 前端性能：浏览器开发者工具

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置
   - 确认防火墙设置

2. **前端无法访问后端API**
   - 检查后端服务是否启动
   - 验证代理配置
   - 检查跨域设置

3. **编译错误**
   - 检查Java版本
   - 清理Maven缓存：`mvn clean`
   - 检查依赖版本冲突

### 日志分析
```bash
# 查看后端日志
tail -f logs/metadata-system.log

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

## 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

2. **应用安全**
   - 配置HTTPS
   - 实施访问控制
   - 定期更新依赖

3. **网络安全**
   - 配置防火墙
   - 使用VPN或内网访问
   - 监控异常访问

---

如有问题，请查看项目README.md或联系开发团队。
