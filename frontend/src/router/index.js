import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { title: '仪表盘' }
  },
  {
    path: '/platform',
    name: 'Platform',
    component: () => import('@/views/platform/PlatformList.vue'),
    meta: { title: '平台管理' }
  },
  {
    path: '/datatype',
    name: 'DataType',
    component: () => import('@/views/datatype/DataTypeList.vue'),
    meta: { title: '数据类型管理' }
  },
  {
    path: '/field',
    name: 'Field',
    component: () => import('@/views/field/FieldList.vue'),
    meta: { title: '标准字段管理' }
  },
  {
    path: '/mapping',
    name: 'Mapping',
    component: () => import('@/views/mapping/MappingList.vue'),
    meta: { title: '字段映射管理' }
  },
  {
    path: '/query',
    name: 'Query',
    component: () => import('@/views/query/QueryCenter.vue'),
    meta: { title: '查询中心' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 元数据管理系统`
  }
  next()
})

export default router
