// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

// 布局样式
.layout-container {
  display: flex;
  height: 100vh;
  
  .sidebar {
    width: 250px;
    background: #fff;
    border-right: 1px solid #e4e7ed;
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.08);
    
    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #e4e7ed;
      font-size: 18px;
      font-weight: 600;
      color: #409eff;
    }
    
    .menu-container {
      height: calc(100vh - 60px);
      overflow-y: auto;
    }
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .header {
      height: 60px;
      background: #fff;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      box-shadow: 0 2px 4px rgba(0, 21, 41, 0.08);
      
      .breadcrumb {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background: #f5f7fa;
    }
  }
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e4e7ed;
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
}

// 表格样式
.table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-box {
      width: 300px;
    }
  }
}

// 状态标签样式
.status-tag {
  &.active {
    background: #f0f9ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }
  
  &.inactive {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
}

// 工具提示样式
.tooltip-content {
  max-width: 300px;
  word-break: break-all;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-container {
    .sidebar {
      width: 200px;
    }
    
    .main-content {
      .content {
        padding: 15px;
      }
    }
  }
  
  .card {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .table-container {
    .table-header {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
      
      .search-box {
        width: 100%;
      }
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
