<template>
  <div class="datatype-list">
    <div class="card">
      <div class="card-header">
        <div class="card-title">数据类型管理</div>
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          新增数据类型
        </el-button>
      </div>

      <div class="coming-soon">
        <el-icon><Collection /></el-icon>
        <h3>数据类型管理功能开发中...</h3>
        <p>该功能将支持管理订单、退单、商品、类目等数据类型</p>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.datatype-list {
  max-width: 1200px;
  margin: 0 auto;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.coming-soon h3 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
