<template>
  <div class="mapping-list">
    <div class="card">
      <div class="card-header">
        <div class="card-title">字段映射管理</div>
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          新增字段映射
        </el-button>
      </div>

      <div class="coming-soon">
        <el-icon><Connection /></el-icon>
        <h3>字段映射管理功能开发中...</h3>
        <p>该功能将支持配置不同平台字段到标准字段的映射关系</p>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.mapping-list {
  max-width: 1200px;
  margin: 0 auto;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.coming-soon h3 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
