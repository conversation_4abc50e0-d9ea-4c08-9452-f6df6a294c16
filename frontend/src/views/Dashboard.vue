<template>
  <Layout>
    <div class="dashboard">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon platform">
            <el-icon><Platform /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.platformCount }}</div>
            <div class="stat-label">支持平台</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon datatype">
            <el-icon><Collection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.dataTypeCount }}</div>
            <div class="stat-label">数据类型</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon field">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.fieldCount }}</div>
            <div class="stat-label">标准字段</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon mapping">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.mappingCount }}</div>
            <div class="stat-label">字段映射</div>
          </div>
        </div>
      </div>
      
      <!-- 快速操作 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">快速操作</div>
        </div>
        <div class="quick-actions">
          <el-button type="primary" @click="$router.push('/platform')">
            <el-icon><Platform /></el-icon>
            管理平台
          </el-button>
          <el-button type="success" @click="$router.push('/field')">
            <el-icon><Grid /></el-icon>
            配置字段
          </el-button>
          <el-button type="warning" @click="$router.push('/mapping')">
            <el-icon><Connection /></el-icon>
            设置映射
          </el-button>
          <el-button type="info" @click="$router.push('/query')">
            <el-icon><Search /></el-icon>
            查询数据
          </el-button>
        </div>
      </div>
      
      <!-- 最近活动 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">最近活动</div>
        </div>
        <div class="activity-list">
          <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
            <div class="activity-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 系统信息 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">系统信息</div>
        </div>
        <div class="system-info">
          <div class="info-item">
            <span class="info-label">系统版本：</span>
            <span class="info-value">v1.0.0</span>
          </div>
          <div class="info-item">
            <span class="info-label">最后更新：</span>
            <span class="info-value">{{ new Date().toLocaleDateString() }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">运行状态：</span>
            <el-tag type="success">正常</el-tag>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Layout from '@/components/Layout.vue'

// 统计数据
const stats = ref({
  platformCount: 0,
  dataTypeCount: 0,
  fieldCount: 0,
  mappingCount: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新增淘宝平台配置',
    time: '2小时前'
  },
  {
    id: 2,
    title: '更新订单字段映射',
    time: '4小时前'
  },
  {
    id: 3,
    title: '添加商品标准字段',
    time: '1天前'
  },
  {
    id: 4,
    title: '配置京东API接口',
    time: '2天前'
  }
])

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用实际的API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      platformCount: 4,
      dataTypeCount: 5,
      fieldCount: 18,
      mappingCount: 32
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stat-icon.platform {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.datatype {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.field {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.mapping {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  background: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #909399;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #303133;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .quick-actions .el-button {
    flex: 1;
    min-width: 120px;
  }
}
</style>
