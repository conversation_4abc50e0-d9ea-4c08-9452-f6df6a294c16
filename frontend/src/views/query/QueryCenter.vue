<template>
  <Layout>
    <div class="query-center">
      <div class="card">
        <div class="card-header">
          <div class="card-title">查询中心</div>
        </div>
        
        <div class="coming-soon">
          <el-icon><Search /></el-icon>
          <h3>查询中心功能开发中...</h3>
          <p>该功能将提供平台能力查询、字段对比、API文档等服务</p>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.query-center {
  max-width: 1200px;
  margin: 0 auto;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.coming-soon h3 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
