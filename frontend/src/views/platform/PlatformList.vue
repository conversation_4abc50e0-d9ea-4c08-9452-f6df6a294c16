<template>
  <Layout>
    <div class="platform-list">
      <div class="card">
        <div class="card-header">
          <div class="card-title">平台管理</div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增平台
          </el-button>
        </div>
        
        <div class="table-container">
          <div class="table-header">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索平台名称、编码或描述"
              class="search-box"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <el-table
            :data="tableData"
            v-loading="loading"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column prop="platformCode" label="平台编码" width="120" />
            <el-table-column prop="platformName" label="平台名称" width="150" />
            <el-table-column prop="platformDesc" label="平台描述" show-overflow-tooltip />
            <el-table-column prop="apiVersion" label="API版本" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  size="small" 
                  @click="handleToggleStatus(row)"
                >
                  {{ row.status === 1 ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
      
      <!-- 新增/编辑对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="handleDialogClose"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="平台编码" prop="platformCode">
            <el-input v-model="form.platformCode" placeholder="请输入平台编码" />
          </el-form-item>
          <el-form-item label="平台名称" prop="platformName">
            <el-input v-model="form.platformName" placeholder="请输入平台名称" />
          </el-form-item>
          <el-form-item label="平台描述" prop="platformDesc">
            <el-input
              v-model="form.platformDesc"
              type="textarea"
              :rows="3"
              placeholder="请输入平台描述"
            />
          </el-form-item>
          <el-form-item label="API版本" prop="apiVersion">
            <el-input v-model="form.apiVersion" placeholder="请输入API版本" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { 
  getPlatformPage, 
  createPlatform, 
  updatePlatform, 
  deletePlatform, 
  updatePlatformStatus 
} from '@/api/platform'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  platformCode: '',
  platformName: '',
  platformDesc: '',
  apiVersion: '',
  status: 1
})

// 表单验证规则
const formRules = {
  platformCode: [
    { required: true, message: '请输入平台编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '平台编码只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  platformName: [
    { required: true, message: '请输入平台名称', trigger: 'blur' }
  ],
  apiVersion: [
    { required: true, message: '请输入API版本', trigger: 'blur' }
  ]
}

// 对话框标题
const dialogTitle = computed(() => {
  return form.id ? '编辑平台' : '新增平台'
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword
    }
    
    const response = await getPlatformPage(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

// 新增
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  Object.assign(form, row)
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除平台 "${row.platformName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deletePlatform(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 切换状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}平台 "${row.platformName}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await updatePlatformStatus(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    if (form.id) {
      await updatePlatform(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await createPlatform(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    platformCode: '',
    platformName: '',
    platformDesc: '',
    apiVersion: '',
    status: 1
  })
  formRef.value?.clearValidate()
}

// 对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.platform-list {
  max-width: 1200px;
  margin: 0 auto;
}

.table-header {
  margin-bottom: 20px;
}

.search-box {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .search-box {
    width: 100%;
  }
}
</style>
