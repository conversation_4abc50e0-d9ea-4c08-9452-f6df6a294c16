import request from '@/utils/request'

// 平台管理API

/**
 * 分页查询平台列表
 */
export function getPlatformPage(params) {
  return request({
    url: '/platform/page',
    method: 'get',
    params
  })
}

/**
 * 查询所有启用的平台
 */
export function getPlatformList() {
  return request({
    url: '/platform/list',
    method: 'get'
  })
}

/**
 * 根据ID查询平台详情
 */
export function getPlatformById(id) {
  return request({
    url: `/platform/${id}`,
    method: 'get'
  })
}

/**
 * 根据平台编码查询平台信息
 */
export function getPlatformByCode(platformCode) {
  return request({
    url: `/platform/code/${platformCode}`,
    method: 'get'
  })
}

/**
 * 创建平台
 */
export function createPlatform(data) {
  return request({
    url: '/platform',
    method: 'post',
    data
  })
}

/**
 * 更新平台
 */
export function updatePlatform(id, data) {
  return request({
    url: `/platform/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除平台
 */
export function deletePlatform(id) {
  return request({
    url: `/platform/${id}`,
    method: 'delete'
  })
}

/**
 * 更新平台状态
 */
export function updatePlatformStatus(id, status) {
  return request({
    url: `/platform/${id}/status`,
    method: 'put',
    params: { status }
  })
}
