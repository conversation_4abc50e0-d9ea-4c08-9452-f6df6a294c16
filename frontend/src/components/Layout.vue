<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="logo">
        <el-icon><DataBoard /></el-icon>
        <span style="margin-left: 8px;">元数据管理</span>
      </div>
      
      <div class="menu-container">
        <el-menu
          :default-active="activeMenu"
          router
          :collapse="false"
          background-color="#fff"
          text-color="#333"
          active-text-color="#409eff"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <el-menu-item index="/platform">
            <el-icon><Platform /></el-icon>
            <span>平台管理</span>
          </el-menu-item>
          
          <el-menu-item index="/datatype">
            <el-icon><Collection /></el-icon>
            <span>数据类型</span>
          </el-menu-item>
          
          <el-menu-item index="/field">
            <el-icon><Grid /></el-icon>
            <span>标准字段</span>
          </el-menu-item>
          
          <el-menu-item index="/mapping">
            <el-icon><Connection /></el-icon>
            <span>字段映射</span>
          </el-menu-item>
          
          <el-menu-item index="/query">
            <el-icon><Search /></el-icon>
            <span>查询中心</span>
          </el-menu-item>
        </el-menu>
      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 头部 -->
      <div class="header">
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="refreshPage">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <!-- 内容区 -->
      <div class="content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '元数据管理系统'
})

// 刷新页面
const refreshPage = () => {
  router.go(0)
}
</script>

<style scoped>
.layout-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 250px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.08);
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e4e7ed;
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.menu-container {
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 21, 41, 0.08);
}

.breadcrumb {
  font-size: 16px;
  font-weight: 500;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f7fa;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: #ecf5ff !important;
}
</style>
