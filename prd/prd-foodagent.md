# 背景
客户是一个食品售卖公司。工作流程中：设计部门会输出食品包装设计稿, 然后交给审核部门进行审核, 审核部门会对照法规内容确认设计稿是否符合法规要求, 然后输出修改意见及报告, 返回给设计部门进行调整。客户需要做一个基于AI的智能审核系统。

# 功能明细
- 知识库管理。支持上传法规文件, 作为参考, 供审核部门使用。文档类型支持：pdf、word。属性包含：文档类型、文档名称、文档版本、生效日期、状态。操作类型支持：上传、修改、删除、查询(分页)。前端效果参考：知识库.png、添加法规.png、修改法规.png
- 审核工作台。支持发起新的审核任务, 浏览审核任务列表。审核任务属性：名称、日期、状态(待审核、审核中、已完成、审核失败)。
  - 发起审核任务。需要填写：审核名称、选择品牌、选择法规文件、忽略描述信息、文件版本、以及上传审核文件(支持pdf、word、excel、ppt、pdf及图片)。前端效果参考：审核任务列表.png、发起审核任务.png
  - 列表中对于已经完成的任务，支持查看报告，点击查看，显示报告页面。
  - 审核报告为一个问题列表。每一项包含：1. 风险等级<高、中、低>. 2.问题描述. 3. 参考法规. 4. 修改建议。 
- 版本差异对比。支持图片对比、图文对比。支持任务列表及结果查看。
  - 图片对比。上传2个图片, 提交对比任务。任务属性：日期、状态(待对比、对比中、已完成、失败)
  - 图文对比。左侧为文档，右边为图片，提交对比任务。任务属性：日期、状态(待对比、对比中、已完成、失败)

# 后端技术实现说明
- 设计字典, 支持配置。比如知识库的文档类型、审核文件类型、品牌。
- 设计任务表，支持3类任务。审核任务、图片对比任务、图文对比任务。客户端提交均为任务, 进入任务表, 后台通过调度来进行实际的任务执行。
- 设计任务执行器, 支持3类任务。审核任务、图片对比任务、图文对比任务。任务并发度可配置。
- 任务表设计任务执行结果字段。任务执行完成成功后，结果写回。
- 知识库上传文档操作步骤：
  - 上传文档到oss。
  - 调用阿里云百炼api，AddFilesFromAuthorizedOss
  - 对文件进行打标。调用百炼api。UpdateFileTag
- 检测任务操作步骤：
  - 判断文件类型。调用文档内容解析工具，获取文件内容列表及对应的坐标(图片的话)，文档只获取对应的内容列表。
  - 对于每一个元素，重复执行以下步骤：
    - 调用百炼api，进行指定知识库的rag
    - 组装提示词，调用模型qwen3，输出检测报告。报告为一个列表，包含：1. 风险等级<高、中、低>. 2.问题描述. 3. 参考法规. 4. 建议修改意见。
- 图图对比任务操作步骤：
- 图文对比任务操作步骤：


# 前端模块说明
1. 登录页面。支持用户名密码登录。
2. 登陆后的效果：左侧为菜单, 可收起展开. 顶部固定为消息提醒和用户信息。其他部分为主功能区。
  - 菜单1：审核工作台
  - 菜单2：版本差异对比
  - 菜单3：知识库管理
  - 菜单4：系统管理。管理字典信息。

# 技术要求
1. 后端采用springboot, 数据库为mysql, ORM框架采用mybatis-plus
2. 所有文件上传后端存储均为oss, 本地数据库只保留oss的链接。
2. 前端采用vue架构。